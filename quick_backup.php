<?php
/**
 * 快速备份脚本
 * 使用方法：php quick_backup.php
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

$backupType = $argv[1] ?? 'full'; // 默认使用完整备份
$backupTypeName = $backupType === 'full' ? '完整备份' : '快速备份';

echo "=== {$backupTypeName}工具 ===\n";
echo "备份类型: $backupTypeName\n";
echo "开始时间: " . date('Y-m-d H:i:s') . "\n\n";

// 配置
$timestamp = date('Y-m-d_H-i-s');
$backupDir = __DIR__ . '/Backup';
$currentBackupDir = $backupDir . '/' . $timestamp;

// 数据库配置
$dbConfig = [
    'host' => 'shuju-mysql.ns-qqzrupud.svc',
    'port' => '3306',
    'name' => 'shuju',
    'user' => 'root',
    'pass' => 'gb5bjqq9'
];

// 创建备份目录
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
    echo "✓ 创建备份根目录\n";
}

if (!is_dir($currentBackupDir)) {
    mkdir($currentBackupDir, 0755, true);
    echo "✓ 创建当前备份目录: $timestamp\n";
}

// 1. 备份数据库
echo "\n1. 备份数据库...\n";
$sqlFile = $currentBackupDir . '/database.sql';

$command = sprintf(
    'mysqldump -h%s -P%s -u%s -p%s %s > %s 2>&1',
    escapeshellarg($dbConfig['host']),
    escapeshellarg($dbConfig['port']),
    escapeshellarg($dbConfig['user']),
    escapeshellarg($dbConfig['pass']),
    escapeshellarg($dbConfig['name']),
    escapeshellarg($sqlFile)
);

exec($command, $output, $returnCode);

if ($returnCode === 0 && file_exists($sqlFile) && filesize($sqlFile) > 0) {
    $size = round(filesize($sqlFile) / 1024 / 1024, 2);
    echo "   ✓ 数据库备份成功 ({$size} MB)\n";
} else {
    echo "   ✗ 数据库备份失败\n";
    if (!empty($output)) {
        echo "   错误: " . implode("\n", $output) . "\n";
    }
}

// 2. 备份重要文件
echo "\n2. 备份重要文件...\n";

// 根据备份类型选择文件（使用之前定义的 $backupType）

if ($backupType === 'full') {
    // 完整备份 - 备份所有重要文件
    $importantFiles = [
        'index.html' => '主页面',
        'api_direct.php' => 'API接口',
        'backend/' => '后端目录',
        '.htaccess' => '重写规则',
        'README.md' => '说明文档',
        'PROJECT_SUMMARY.md' => '项目总结',
        'API_DOCUMENTATION.md' => 'API文档',
        'welcome.html' => '欢迎页面',
        '404.html' => '错误页面',
        'install.php' => '安装脚本',
        'deploy.sh' => '部署脚本',
        'entrypoint.sh' => '入口脚本',
        'backup.php' => '完整备份脚本',
        'backup_api.php' => '备份API',
        'backup_manager.html' => '备份管理界面',
        'restore.php' => '恢复脚本',
        'auto_backup.sh' => '自动备份脚本'
    ];
} else {
    // 快速备份 - 只备份核心文件
    $importantFiles = [
        'index.html' => '主页面',
        'api_direct.php' => 'API接口',
        'backend/' => '后端目录',
        '.htaccess' => '重写规则',
        'README.md' => '说明文档'
    ];
}

$copiedCount = 0;
foreach ($importantFiles as $file => $description) {
    $sourcePath = __DIR__ . '/' . $file;
    $destPath = $currentBackupDir . '/' . $file;

    if (file_exists($sourcePath)) {
        if (is_dir($sourcePath)) {
            // 复制目录
            copyDirectory($sourcePath, $destPath);
            echo "   ✓ 复制目录: $file ($description)\n";
        } else {
            // 复制文件
            $destDir = dirname($destPath);
            if (!is_dir($destDir)) {
                mkdir($destDir, 0755, true);
            }
            copy($sourcePath, $destPath);
            echo "   ✓ 复制文件: $file ($description)\n";
        }
        $copiedCount++;
    } else {
        echo "   - 跳过不存在的文件: $file\n";
    }
}

// 3. 创建备份说明
echo "\n3. 创建备份说明...\n";

$backupInfo = [
    'backup_time' => date('Y-m-d H:i:s'),
    'backup_type' => $backupType,
    'database_file' => 'database.sql',
    'files_copied' => $copiedCount,
    'notes' => $backupType === 'full' ? '完整备份，包含数据库和所有重要文件' : '快速备份，包含数据库和核心文件'
];

$infoFile = $currentBackupDir . '/backup_info.txt';
$infoContent = "备份信息\n";
$infoContent .= "========\n";
$infoContent .= "备份时间: {$backupInfo['backup_time']}\n";
$infoContent .= "备份类型: {$backupInfo['backup_type']}\n";
$infoContent .= "数据库文件: {$backupInfo['database_file']}\n";
$infoContent .= "复制文件数: {$backupInfo['files_copied']}\n";
$infoContent .= "说明: {$backupInfo['notes']}\n\n";

$infoContent .= "恢复说明\n";
$infoContent .= "========\n";
$infoContent .= "1. 恢复数据库: mysql -u用户名 -p密码 数据库名 < database.sql\n";
$infoContent .= "2. 复制文件到网站根目录\n";
$infoContent .= "3. 检查文件权限\n";

file_put_contents($infoFile, $infoContent);
echo "   ✓ 备份说明创建完成\n";

// 4. 显示备份结果
echo "\n=== 备份完成 ===\n";
echo "备份位置: $currentBackupDir\n";

// 计算备份大小
$totalSize = 0;
$iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($currentBackupDir));
foreach ($iterator as $file) {
    if ($file->isFile()) {
        $totalSize += $file->getSize();
    }
}

$totalSizeMB = round($totalSize / 1024 / 1024, 2);
echo "备份大小: {$totalSizeMB} MB\n";

// 列出备份内容
echo "\n备份内容:\n";
$files = scandir($currentBackupDir);
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        $filePath = $currentBackupDir . '/' . $file;
        if (is_dir($filePath)) {
            echo "  📁 $file/\n";
        } else {
            $fileSize = round(filesize($filePath) / 1024, 2);
            echo "  📄 $file ({$fileSize} KB)\n";
        }
    }
}

echo "\n✓ {$backupTypeName}完成！\n";

/**
 * 递归复制目录
 */
function copyDirectory($source, $destination) {
    if (!is_dir($destination)) {
        mkdir($destination, 0755, true);
    }

    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::SELF_FIRST
    );

    foreach ($iterator as $item) {
        $destPath = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();

        if ($item->isDir()) {
            if (!is_dir($destPath)) {
                mkdir($destPath, 0755, true);
            }
        } else {
            $destDir = dirname($destPath);
            if (!is_dir($destDir)) {
                mkdir($destDir, 0755, true);
            }
            copy($item, $destPath);
        }
    }
}
?>
