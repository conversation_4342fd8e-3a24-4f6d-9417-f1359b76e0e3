<?php
/**
 * 备份下载脚本 - 解决ZIP扩展问题
 */

// 安全检查
if (!isset($_GET['backup'])) {
    die('错误：未指定备份名称');
}

$backupName = $_GET['backup'];
$backupDir = __DIR__ . '/Backup';
$backupPath = $backupDir . '/' . $backupName;

// 安全检查
if (!file_exists($backupPath) || strpos(realpath($backupPath), realpath($backupDir)) !== 0) {
    die('错误：备份不存在或路径无效');
}

/**
 * 格式化字节大小
 */
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    return round($bytes, $precision) . ' ' . $units[$i];
}

if (is_dir($backupPath)) {
    // 目录备份 - 尝试多种压缩方式
    
    // 方案1：尝试使用tar命令（最常用）
    $tarFile = sys_get_temp_dir() . '/' . $backupName . '.tar.gz';
    
    // 如果临时目录不可写，使用项目目录
    if (!is_writable(sys_get_temp_dir())) {
        $tempDir = __DIR__ . '/temp';
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }
        $tarFile = $tempDir . '/' . $backupName . '.tar.gz';
    }
    
    $command = sprintf(
        'cd %s && tar -czf %s %s 2>/dev/null',
        escapeshellarg(dirname($backupPath)),
        escapeshellarg($tarFile),
        escapeshellarg(basename($backupPath))
    );
    
    exec($command, $output, $returnCode);
    
    if ($returnCode === 0 && file_exists($tarFile)) {
        // 成功创建tar.gz文件
        header('Content-Type: application/gzip');
        header('Content-Disposition: attachment; filename="' . $backupName . '.tar.gz"');
        header('Content-Length: ' . filesize($tarFile));
        
        // 输出文件并删除临时文件
        readfile($tarFile);
        unlink($tarFile);
        exit;
    }
    
    // 方案2：尝试使用zip命令
    $zipFile = str_replace('.tar.gz', '.zip', $tarFile);
    $command = sprintf(
        'cd %s && zip -r %s %s 2>/dev/null',
        escapeshellarg(dirname($backupPath)),
        escapeshellarg($zipFile),
        escapeshellarg(basename($backupPath))
    );
    
    exec($command, $output, $returnCode);
    
    if ($returnCode === 0 && file_exists($zipFile)) {
        // 成功创建zip文件
        header('Content-Type: application/zip');
        header('Content-Disposition: attachment; filename="' . $backupName . '.zip"');
        header('Content-Length: ' . filesize($zipFile));
        
        readfile($zipFile);
        unlink($zipFile);
        exit;
    }
    
    // 方案3：创建详细的文件清单（备用方案）
    $listFile = str_replace('.zip', '_详细清单.txt', $zipFile);
    
    $fileList = "=== 备份详细清单 ===\n";
    $fileList .= "备份名称: $backupName\n";
    $fileList .= "创建时间: " . date('Y-m-d H:i:s', filemtime($backupPath)) . "\n";
    $fileList .= "下载时间: " . date('Y-m-d H:i:s') . "\n\n";
    
    // 统计信息
    $totalFiles = 0;
    $totalSize = 0;
    
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($backupPath, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    $fileList .= "=== 文件列表 ===\n";
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $relativePath = substr($file->getPathname(), strlen($backupPath) + 1);
            $size = $file->getSize();
            $totalSize += $size;
            $totalFiles++;
            
            $fileList .= sprintf(
                "%-50s %10s %s\n",
                $relativePath,
                formatBytes($size),
                date('Y-m-d H:i:s', $file->getMTime())
            );
        }
    }
    
    $fileList .= "\n=== 统计信息 ===\n";
    $fileList .= "总文件数: $totalFiles\n";
    $fileList .= "总大小: " . formatBytes($totalSize) . "\n\n";
    
    $fileList .= "=== 重要文件内容预览 ===\n";
    
    // 显示重要文件的内容
    $importantFiles = ['database.sql', 'backup_info.txt', 'index.html'];
    foreach ($importantFiles as $fileName) {
        $filePath = $backupPath . '/' . $fileName;
        if (file_exists($filePath)) {
            $fileList .= "\n--- $fileName ---\n";
            $content = file_get_contents($filePath);
            
            // 限制显示长度
            if (strlen($content) > 2000) {
                $fileList .= substr($content, 0, 2000) . "\n... (文件太长，已截断) ...\n";
            } else {
                $fileList .= $content . "\n";
            }
        }
    }
    
    $fileList .= "\n=== 说明 ===\n";
    $fileList .= "由于服务器不支持ZIP压缩，此文件包含了备份的详细信息。\n";
    $fileList .= "要获取完整备份，请联系管理员或使用服务器端工具。\n";
    $fileList .= "备份位置: $backupPath\n";
    
    file_put_contents($listFile, $fileList);
    
    // 下载文件清单
    header('Content-Type: text/plain; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $backupName . '_详细清单.txt"');
    header('Content-Length: ' . filesize($listFile));
    
    readfile($listFile);
    unlink($listFile);
    
} else {
    // 单个文件 - 直接下载
    $mimeType = 'application/octet-stream';
    $extension = pathinfo($backupName, PATHINFO_EXTENSION);
    
    switch ($extension) {
        case 'zip':
            $mimeType = 'application/zip';
            break;
        case 'sql':
            $mimeType = 'text/plain';
            break;
        case 'txt':
            $mimeType = 'text/plain';
            break;
        case 'gz':
        case 'tar':
            $mimeType = 'application/gzip';
            break;
    }
    
    header('Content-Type: ' . $mimeType);
    header('Content-Disposition: attachment; filename="' . basename($backupName) . '"');
    header('Content-Length: ' . filesize($backupPath));
    
    readfile($backupPath);
}
?>
