<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库导入工具 - 宝塔LNMP兼容</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .problem-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .solution-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .code-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 10px 10px 0;
            font-size: 16px;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .download-btn.success {
            background: #28a745;
        }
        .download-btn.warning {
            background: #ffc107;
            color: #212529;
        }
        .step {
            background: #e9ecef;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #007bff;
        }
        .warning {
            color: #856404;
            font-weight: bold;
        }
        .success {
            color: #155724;
            font-weight: bold;
        }
        .error {
            color: #721c24;
            font-weight: bold;
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin: 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 数据库导入工具</h1>
        <p style="text-align: center; color: #666;">解决宝塔LNMP环境MySQL排序规则兼容性问题</p>
        
        <div class="problem-box">
            <h3>❌ 遇到的问题</h3>
            <p><strong>错误信息</strong>：<code>#1273 - Unknown collation: 'utf8mb4_0900_ai_ci'</code></p>
            <p><strong>原因</strong>：备份文件使用了MySQL 8.0的排序规则，但宝塔环境可能使用MySQL 5.7或更早版本。</p>
        </div>

        <div class="solution-box">
            <h3>✅ 解决方案</h3>
            <p>我们提供了兼容的数据库脚本，使用 <code>utf8mb4_unicode_ci</code> 排序规则，兼容所有MySQL版本。</p>
        </div>

        <h2>📥 选择导入方式</h2>

        <div class="step">
            <h3>方案一：简化版本（推荐新用户）</h3>
            <p>包含基础表结构和示例数据，适合新部署：</p>
            <ul>
                <li>✅ 1个管理员用户（admin/password）</li>
                <li>✅ 1个默认账本</li>
                <li>✅ 3条示例记录</li>
                <li>✅ 完整的表结构和索引</li>
            </ul>
            <a href="database_simple.sql" class="download-btn success" download>📥 下载简化版本</a>
        </div>

        <div class="step">
            <h3>方案二：完整版本（推荐迁移用户）</h3>
            <p>包含所有原始数据，适合数据迁移：</p>
            <ul>
                <li>✅ 3个用户账号</li>
                <li>✅ 5个账本</li>
                <li>✅ 57条完整记录</li>
                <li>✅ 完整的月份状态数据</li>
            </ul>
            <a href="database_compatible.sql" class="download-btn" download>📥 下载完整版本</a>
            <p class="warning">⚠️ 注意：完整版本文件较大，如果导入失败请使用简化版本</p>
        </div>

        <h2>🚀 导入步骤</h2>

        <div class="step">
            <h3>步骤1：在宝塔面板创建数据库</h3>
            <ol>
                <li>登录宝塔面板</li>
                <li>进入 <strong>数据库</strong> → <strong>添加数据库</strong></li>
                <li>数据库名：<code>shuju</code>（或自定义）</li>
                <li>用户名：<code>root</code>（或自定义）</li>
                <li>设置安全密码</li>
            </ol>
        </div>

        <div class="step">
            <h3>步骤2：导入数据库</h3>
            <p><strong>方法A：使用phpMyAdmin（推荐）</strong></p>
            <ol>
                <li>在宝塔面板点击数据库的 <strong>管理</strong> 按钮</li>
                <li>进入phpMyAdmin</li>
                <li>选择你的数据库</li>
                <li>点击 <strong>导入</strong> 选项卡</li>
                <li>选择下载的SQL文件</li>
                <li>点击 <strong>执行</strong></li>
            </ol>

            <p><strong>方法B：使用命令行</strong></p>
            <div class="code-box">
mysql -u数据库用户名 -p数据库名 < database_simple.sql
# 或
mysql -u数据库用户名 -p数据库名 < database_compatible.sql
            </div>
        </div>

        <div class="step">
            <h3>步骤3：修改项目配置</h3>
            <p>修改 <code>backend/api/config.php</code> 文件：</p>
            <div class="code-box">
define('DB_HOST', 'localhost');        // 宝塔默认本地数据库
define('DB_NAME', 'your_database_name'); // 你创建的数据库名
define('DB_USER', 'your_username');      // 数据库用户名
define('DB_PASS', 'your_password');      // 数据库密码
            </div>
        </div>

        <div class="step">
            <h3>步骤4：验证导入</h3>
            <p>导入成功后，你应该能看到：</p>
            <ul>
                <li>✅ 4个数据表：users, account_books, records, record_monthly_states</li>
                <li>✅ 管理员账号：用户名 <code>admin</code>，密码 <code>password</code></li>
                <li>✅ 访问网站可以正常登录和使用</li>
            </ul>
        </div>

        <h2>🔍 排查问题</h2>

        <div class="step">
            <h3>如果仍然遇到问题</h3>
            <p><strong>检查MySQL版本</strong>：</p>
            <div class="code-box">SELECT VERSION();</div>
            
            <p><strong>检查支持的排序规则</strong>：</p>
            <div class="code-box">SHOW COLLATION LIKE 'utf8mb4%';</div>
            
            <p><strong>如果还是不行，尝试最基础的版本</strong>：</p>
            <ol>
                <li>手动创建表结构（不包含排序规则）</li>
                <li>逐个插入数据</li>
                <li>联系技术支持</li>
            </ol>
        </div>

        <h2>📞 技术支持</h2>
        <div class="step">
            <p>如果遇到任何问题，请提供以下信息：</p>
            <ul>
                <li>🔹 宝塔面板版本</li>
                <li>🔹 MySQL版本</li>
                <li>🔹 PHP版本</li>
                <li>🔹 具体错误信息</li>
                <li>🔹 使用的导入方法</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <p class="success">🎉 导入成功后，你就可以开始使用完整的记账系统了！</p>
            <p>默认登录信息：用户名 <strong>admin</strong>，密码 <strong>password</strong></p>
        </div>
    </div>
</body>
</html>
