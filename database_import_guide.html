<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📚 完整数据库导入指南 - 宝塔LNMP兼容</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 { color: #333; }
        .highlight-box {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
        }
        .success-box {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fff3e0;
            border-left: 4px solid #ff9800;
            padding: 15px;
            margin: 20px 0;
        }
        .download-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 10px 10px 0;
            font-size: 16px;
            font-weight: bold;
        }
        .download-btn:hover { background: #45a049; }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .step {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 15px 0;
        }
        .step h3 {
            margin-top: 0;
            color: #495057;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th { background: #f8f9fa; font-weight: bold; }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📚 完整数据库备份 - 宝塔LNMP兼容版本</h1>
        
        <div class="highlight-box">
            <h3>🎯 最新完整备份特点</h3>
            <ul>
                <li><strong>✅ 完整数据</strong>：3个用户 + 5个账本 + 64条记录 + 68个月份状态</li>
                <li><strong>✅ 宝塔兼容</strong>：使用 utf8mb4_unicode_ci 排序规则，兼容所有MySQL版本</li>
                <li><strong>✅ 性能优化</strong>：集成了所有性能优化索引</li>
                <li><strong>✅ 数据完整</strong>：保持原有数据的完整性和关联关系</li>
                <li><strong>✅ 即用即部署</strong>：导入后立即可以使用所有功能</li>
            </ul>
        </div>

        <h2>📥 下载完整数据库备份</h2>
        <div style="text-align: center; margin: 30px 0;">
            <a href="database_full_compatible.sql" class="download-btn" download>
                📥 下载完整兼容数据库 (database_full_compatible.sql)
            </a>
        </div>

        <h2>📊 数据统计</h2>
        <table>
            <tr><th>数据类型</th><th>数量</th><th>说明</th></tr>
            <tr><td>👥 用户账号</td><td>3个</td><td>admin, Maggie, Seven</td></tr>
            <tr><td>📚 账本</td><td>5个</td><td>默认账本、个人账本、烂账等</td></tr>
            <tr><td>📝 记录</td><td>64条</td><td>包含各种类型的财务记录</td></tr>
            <tr><td>📅 月份状态</td><td>68个</td><td>历史月份的完成状态</td></tr>
            <tr><td>🔧 索引</td><td>15个</td><td>性能优化索引</td></tr>
        </table>

        <h2>🚀 导入步骤</h2>

        <div class="step">
            <h3>步骤1：在宝塔面板创建数据库</h3>
            <ol>
                <li>登录宝塔面板</li>
                <li>进入 <strong>数据库</strong> → <strong>添加数据库</strong></li>
                <li>数据库名：<code>shuju</code>（推荐）或自定义</li>
                <li>用户名：<code>root</code> 或自定义</li>
                <li>设置安全密码并记录</li>
            </ol>
        </div>

        <div class="step">
            <h3>步骤2：导入数据库文件</h3>
            
            <h4>方法A：使用phpMyAdmin（推荐）</h4>
            <ol>
                <li>在宝塔面板点击数据库的 <strong>管理</strong> 按钮</li>
                <li>进入phpMyAdmin管理界面</li>
                <li>选择你创建的数据库</li>
                <li>点击 <strong>导入</strong> 选项卡</li>
                <li>点击 <strong>选择文件</strong>，选择 <code>database_full_compatible.sql</code></li>
                <li>确保字符集选择为 <strong>utf8mb4_unicode_ci</strong></li>
                <li>点击 <strong>执行</strong> 开始导入</li>
            </ol>

            <h4>方法B：使用命令行</h4>
            <div class="code">
# 进入文件所在目录
cd /path/to/your/files

# 导入数据库
mysql -u数据库用户名 -p数据库名 < database_full_compatible.sql

# 示例
mysql -uroot -p shuju < database_full_compatible.sql
            </div>
        </div>

        <div class="step">
            <h3>步骤3：修改项目配置</h3>
            <p>编辑 <code>backend/api/config.php</code> 文件：</p>
            <div class="code">
&lt;?php
// 数据库配置
define('DB_HOST', 'localhost');        // 宝塔默认本地数据库
define('DB_NAME', 'shuju');            // 你创建的数据库名
define('DB_USER', 'root');             // 数据库用户名
define('DB_PASS', 'your_password');    // 你设置的数据库密码
define('DB_CHARSET', 'utf8mb4');
?&gt;
            </div>
        </div>

        <div class="step">
            <h3>步骤4：验证导入结果</h3>
            <p>导入成功后，你应该看到：</p>
            <ul>
                <li>✅ 4个数据表：users, account_books, records, record_monthly_states</li>
                <li>✅ 每个表都有相应的数据</li>
                <li>✅ 所有索引都已创建</li>
                <li>✅ 外键关系正确建立</li>
            </ul>
            
            <p>可以运行以下SQL验证：</p>
            <div class="code">
SELECT COUNT(*) as user_count FROM users;           -- 应该返回 3
SELECT COUNT(*) as book_count FROM account_books;   -- 应该返回 5  
SELECT COUNT(*) as record_count FROM records;       -- 应该返回 64
SELECT COUNT(*) as state_count FROM record_monthly_states; -- 应该返回 68
            </div>
        </div>

        <h2>🔑 登录信息</h2>
        <div class="success-box">
            <h3>默认管理员账号</h3>
            <ul>
                <li><strong>用户名</strong>：<code>admin</code></li>
                <li><strong>密码</strong>：<code>password</code></li>
                <li><strong>邮箱</strong>：<code><EMAIL></code></li>
            </ul>
            
            <h3>其他测试账号</h3>
            <ul>
                <li><strong>Maggie</strong>：<EMAIL></li>
                <li><strong>Seven</strong>：<EMAIL></li>
            </ul>
            <p><em>注：其他账号密码需要重置或联系原用户</em></p>
        </div>

        <h2>🔧 功能特性</h2>
        <div class="feature-list">
            <div class="feature-item">
                <h4>💰 财务记录</h4>
                <p>完整的收支记录管理，支持分类和备注</p>
            </div>
            <div class="feature-item">
                <h4>📅 月份导航</h4>
                <p>按月查看历史数据，支持月份状态管理</p>
            </div>
            <div class="feature-item">
                <h4>📊 统计分析</h4>
                <p>自动计算累计金额、月度统计等</p>
            </div>
            <div class="feature-item">
                <h4>🔄 递减记账</h4>
                <p>支持递减类型的特殊记账方式</p>
            </div>
            <div class="feature-item">
                <h4>👥 多用户</h4>
                <p>支持多用户独立账本管理</p>
            </div>
            <div class="feature-item">
                <h4>💾 备份恢复</h4>
                <p>完整的数据备份和恢复功能</p>
            </div>
        </div>

        <h2>⚠️ 注意事项</h2>
        <div class="warning-box">
            <ul>
                <li><strong>备份现有数据</strong>：导入前请备份现有数据库</li>
                <li><strong>检查权限</strong>：确保数据库用户有足够的权限</li>
                <li><strong>字符集</strong>：确保使用 utf8mb4 字符集</li>
                <li><strong>版本兼容</strong>：支持 MySQL 5.5+ 和 MariaDB 10.x+</li>
                <li><strong>文件大小</strong>：完整备份文件较大，请确保有足够空间</li>
            </ul>
        </div>

        <h2>🛠️ 故障排除</h2>
        <div class="step">
            <h3>常见问题解决</h3>
            
            <h4>1. 导入失败：排序规则错误</h4>
            <p><strong>解决方案</strong>：确保使用提供的兼容版本，不要使用原始备份文件</p>
            
            <h4>2. 导入失败：文件过大</h4>
            <p><strong>解决方案</strong>：在phpMyAdmin中增加上传限制，或使用命令行导入</p>
            
            <h4>3. 连接失败</h4>
            <p><strong>解决方案</strong>：检查config.php中的数据库配置是否正确</p>
            
            <h4>4. 功能异常</h4>
            <p><strong>解决方案</strong>：确保所有表和索引都已正确创建</p>
        </div>

        <div class="success-box">
            <h3>🎉 导入成功！</h3>
            <p>导入完成后，你将拥有一个功能完整的记账系统，包含：</p>
            <ul>
                <li>✅ 完整的用户和账本数据</li>
                <li>✅ 丰富的财务记录示例</li>
                <li>✅ 历史月份状态数据</li>
                <li>✅ 优化的数据库性能</li>
                <li>✅ 所有功能特性</li>
            </ul>
            <p><strong>立即开始使用你的记账系统吧！</strong></p>
        </div>
    </div>
</body>
</html>
