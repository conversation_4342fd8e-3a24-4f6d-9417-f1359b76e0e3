<?php
/**
 * 网站和数据库备份脚本
 * 使用方法：php backup.php
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 备份配置
$config = [
    'backup_dir' => __DIR__ . '/Backup',
    'db_host' => 'shuju-mysql.ns-qqzrupud.svc',
    'db_port' => '3306',
    'db_name' => 'shuju',
    'db_user' => 'root',
    'db_pass' => 'gb5bjqq9',
    'exclude_dirs' => [
        'Backup',
        '.git',
        'node_modules',
        'vendor',
        'cache',
        'logs'
    ],
    'exclude_files' => [
        '.DS_Store',
        'Thumbs.db',
        '*.log',
        '*.tmp'
    ]
];

class BackupManager {
    private $config;
    private $backupDir;
    private $timestamp;
    
    public function __construct($config) {
        $this->config = $config;
        $this->timestamp = date('Y-m-d_H-i-s');
        $this->backupDir = $config['backup_dir'] . '/' . $this->timestamp;
        
        // 创建备份目录
        $this->createBackupDir();
    }
    
    /**
     * 创建备份目录
     */
    private function createBackupDir() {
        if (!is_dir($this->config['backup_dir'])) {
            mkdir($this->config['backup_dir'], 0755, true);
            echo "✓ 创建备份根目录: {$this->config['backup_dir']}\n";
        }
        
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
            echo "✓ 创建备份目录: {$this->backupDir}\n";
        }
    }
    
    /**
     * 执行完整备份
     */
    public function fullBackup() {
        echo "=== 开始完整备份 ===\n";
        echo "备份时间: " . date('Y-m-d H:i:s') . "\n";
        echo "备份目录: {$this->backupDir}\n\n";
        
        // 1. 备份数据库
        $this->backupDatabase();
        
        // 2. 备份网站文件
        $this->backupWebsite();
        
        // 3. 创建备份信息文件
        $this->createBackupInfo();
        
        // 4. 创建压缩包
        $this->createArchive();
        
        echo "\n=== 备份完成 ===\n";
        echo "备份位置: {$this->backupDir}\n";
        
        // 5. 清理旧备份
        $this->cleanOldBackups();
    }
    
    /**
     * 备份数据库
     */
    private function backupDatabase() {
        echo "1. 正在备份数据库...\n";
        
        $sqlFile = $this->backupDir . '/database_backup.sql';
        
        // 构建mysqldump命令
        $command = sprintf(
            'mysqldump -h%s -P%s -u%s -p%s %s > %s 2>&1',
            escapeshellarg($this->config['db_host']),
            escapeshellarg($this->config['db_port']),
            escapeshellarg($this->config['db_user']),
            escapeshellarg($this->config['db_pass']),
            escapeshellarg($this->config['db_name']),
            escapeshellarg($sqlFile)
        );
        
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0 && file_exists($sqlFile) && filesize($sqlFile) > 0) {
            $size = $this->formatBytes(filesize($sqlFile));
            echo "   ✓ 数据库备份成功: database_backup.sql ({$size})\n";
        } else {
            echo "   ✗ 数据库备份失败\n";
            if (!empty($output)) {
                echo "   错误信息: " . implode("\n", $output) . "\n";
            }
        }
    }
    
    /**
     * 备份网站文件
     */
    private function backupWebsite() {
        echo "2. 正在备份网站文件...\n";
        
        $websiteDir = $this->backupDir . '/website';
        mkdir($websiteDir, 0755, true);
        
        $this->copyDirectory(__DIR__, $websiteDir);
        
        echo "   ✓ 网站文件备份完成\n";
    }
    
    /**
     * 递归复制目录
     */
    private function copyDirectory($source, $destination) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        $copiedFiles = 0;
        $skippedFiles = 0;
        
        foreach ($iterator as $item) {
            $relativePath = substr($item->getPathname(), strlen($source) + 1);
            
            // 检查是否需要排除
            if ($this->shouldExclude($relativePath)) {
                $skippedFiles++;
                continue;
            }
            
            $destPath = $destination . DIRECTORY_SEPARATOR . $relativePath;
            
            if ($item->isDir()) {
                if (!is_dir($destPath)) {
                    mkdir($destPath, 0755, true);
                }
            } else {
                $destDir = dirname($destPath);
                if (!is_dir($destDir)) {
                    mkdir($destDir, 0755, true);
                }
                copy($item->getPathname(), $destPath);
                $copiedFiles++;
            }
        }
        
        echo "   - 复制文件: {$copiedFiles} 个\n";
        echo "   - 跳过文件: {$skippedFiles} 个\n";
    }
    
    /**
     * 检查文件/目录是否应该被排除
     */
    private function shouldExclude($path) {
        // 检查排除目录
        foreach ($this->config['exclude_dirs'] as $excludeDir) {
            if (strpos($path, $excludeDir) === 0) {
                return true;
            }
        }
        
        // 检查排除文件
        foreach ($this->config['exclude_files'] as $excludeFile) {
            if (fnmatch($excludeFile, basename($path))) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 创建备份信息文件
     */
    private function createBackupInfo() {
        echo "3. 创建备份信息文件...\n";
        
        $info = [
            'backup_time' => date('Y-m-d H:i:s'),
            'backup_type' => 'full',
            'database' => [
                'host' => $this->config['db_host'],
                'name' => $this->config['db_name'],
                'backup_file' => 'database_backup.sql'
            ],
            'website' => [
                'source_path' => __DIR__,
                'backup_path' => 'website/',
                'excluded_dirs' => $this->config['exclude_dirs'],
                'excluded_files' => $this->config['exclude_files']
            ],
            'php_version' => PHP_VERSION,
            'server_info' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
        ];
        
        $infoFile = $this->backupDir . '/backup_info.json';
        file_put_contents($infoFile, json_encode($info, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        
        echo "   ✓ 备份信息文件创建完成: backup_info.json\n";
    }
    
    /**
     * 创建压缩包
     */
    private function createArchive() {
        echo "4. 创建压缩包...\n";
        
        if (!class_exists('ZipArchive')) {
            echo "   ⚠ ZipArchive 扩展未安装，跳过压缩包创建\n";
            return;
        }
        
        $zipFile = $this->config['backup_dir'] . "/backup_{$this->timestamp}.zip";
        $zip = new ZipArchive();
        
        if ($zip->open($zipFile, ZipArchive::CREATE) === TRUE) {
            $this->addDirectoryToZip($zip, $this->backupDir, '');
            $zip->close();
            
            $size = $this->formatBytes(filesize($zipFile));
            echo "   ✓ 压缩包创建完成: backup_{$this->timestamp}.zip ({$size})\n";
            
            // 删除原始备份目录（可选）
            // $this->removeDirectory($this->backupDir);
            // echo "   ✓ 清理临时文件完成\n";
        } else {
            echo "   ✗ 压缩包创建失败\n";
        }
    }
    
    /**
     * 添加目录到ZIP
     */
    private function addDirectoryToZip($zip, $source, $prefix) {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            $relativePath = $prefix . substr($file->getPathname(), strlen($source) + 1);
            
            if ($file->isFile()) {
                $zip->addFile($file->getPathname(), $relativePath);
            }
        }
    }
    
    /**
     * 清理旧备份
     */
    private function cleanOldBackups() {
        echo "5. 清理旧备份...\n";
        
        $backups = glob($this->config['backup_dir'] . '/*');
        $backups = array_filter($backups, 'is_dir');
        
        // 按修改时间排序，保留最新的5个备份
        usort($backups, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        $keepCount = 5;
        $removedCount = 0;
        
        for ($i = $keepCount; $i < count($backups); $i++) {
            $this->removeDirectory($backups[$i]);
            $removedCount++;
        }
        
        // 清理旧的ZIP文件
        $zipFiles = glob($this->config['backup_dir'] . '/backup_*.zip');
        usort($zipFiles, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        for ($i = $keepCount; $i < count($zipFiles); $i++) {
            unlink($zipFiles[$i]);
            $removedCount++;
        }
        
        if ($removedCount > 0) {
            echo "   ✓ 清理了 {$removedCount} 个旧备份\n";
        } else {
            echo "   - 无需清理旧备份\n";
        }
    }
    
    /**
     * 删除目录
     */
    private function removeDirectory($dir) {
        if (!is_dir($dir)) return;
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::CHILD_FIRST
        );
        
        foreach ($iterator as $file) {
            if ($file->isDir()) {
                rmdir($file->getPathname());
            } else {
                unlink($file->getPathname());
            }
        }
        
        rmdir($dir);
    }
    
    /**
     * 格式化字节大小
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

// 执行备份
try {
    $backup = new BackupManager($config);
    $backup->fullBackup();
} catch (Exception $e) {
    echo "备份失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
