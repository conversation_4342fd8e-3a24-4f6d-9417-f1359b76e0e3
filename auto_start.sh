#!/bin/bash

# 数据记账系统 - 自动监控和重启脚本
# 自动检测服务器状态，掉线时自动重启

PORT=8080
PROJECT_DIR="/home/<USER>/project"
CHECK_INTERVAL=10  # 检查间隔（秒）
LOG_FILE="/tmp/auto_server.log"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 清理端口占用
cleanup_port() {
    log_message "🧹 清理端口 $PORT..."
    
    # 方法1: 杀死PHP进程
    pkill -f "php.*-S.*$PORT" 2>/dev/null
    sleep 1
    
    # 方法2: 使用netstat查找并杀死进程
    if command -v netstat >/dev/null 2>&1; then
        # 先尝试普通权限
        PID=$(netstat -tlnp 2>/dev/null | grep ":$PORT " | awk '{print $7}' | cut -d'/' -f1)
        if [ ! -z "$PID" ] && [ "$PID" != "-" ]; then
            log_message "   发现进程 $PID 占用端口，正在清理..."
            kill -9 "$PID" 2>/dev/null
            sleep 1
        else
            # 尝试sudo权限
            PID=$(sudo netstat -tlnp 2>/dev/null | grep ":$PORT " | awk '{print $7}' | cut -d'/' -f1)
            if [ ! -z "$PID" ] && [ "$PID" != "-" ]; then
                log_message "   发现进程 $PID 占用端口，使用sudo清理..."
                sudo kill -9 "$PID" 2>/dev/null
                sleep 1
            fi
        fi
    fi
    
    # 方法3: 使用fuser（如果可用）
    if command -v fuser >/dev/null 2>&1; then
        fuser -k $PORT/tcp 2>/dev/null
        sleep 1
    fi
}

# 启动服务器
start_server() {
    log_message "🚀 启动PHP服务器..."
    
    # 进入项目目录
    cd "$PROJECT_DIR" || {
        log_message "❌ 无法进入项目目录: $PROJECT_DIR"
        return 1
    }
    
    # 启动PHP服务器（后台运行）
    nohup php -d memory_limit=512M -d max_execution_time=300 -S 0.0.0.0:$PORT > /tmp/php_server.log 2>&1 &
    SERVER_PID=$!
    
    # 等待服务器启动
    sleep 3
    
    # 检查服务器是否启动成功
    if ps -p $SERVER_PID > /dev/null 2>&1; then
        log_message "✅ 服务器启动成功，PID: $SERVER_PID"
        log_message "🌐 访问地址: http://localhost:$PORT"
        echo $SERVER_PID > /tmp/php_server.pid
        return 0
    else
        log_message "❌ 服务器启动失败"
        return 1
    fi
}

# 检查服务器状态
check_server() {
    # 方法1: 检查进程是否存在
    if [ -f /tmp/php_server.pid ]; then
        PID=$(cat /tmp/php_server.pid)
        if ps -p $PID > /dev/null 2>&1; then
            # 方法2: 检查HTTP响应
            if curl -s -f "http://localhost:$PORT" > /dev/null 2>&1; then
                return 0  # 服务器正常
            else
                log_message "⚠️  服务器进程存在但无响应"
                return 1  # 服务器无响应
            fi
        else
            log_message "⚠️  服务器进程已停止"
            rm -f /tmp/php_server.pid
            return 1  # 进程不存在
        fi
    else
        return 1  # 没有PID文件
    fi
}

# 停止服务器
stop_server() {
    if [ -f /tmp/php_server.pid ]; then
        PID=$(cat /tmp/php_server.pid)
        log_message "🛑 停止服务器，PID: $PID"
        kill $PID 2>/dev/null
        rm -f /tmp/php_server.pid
    fi
    cleanup_port
}

# 重启服务器
restart_server() {
    log_message "🔄 重启服务器..."
    stop_server
    sleep 2
    cleanup_port
    start_server
}

# 监控循环
monitor_loop() {
    log_message "👀 开始自动监控服务器状态..."
    log_message "📋 检查间隔: ${CHECK_INTERVAL}秒"
    log_message "📝 日志文件: $LOG_FILE"
    log_message "🛑 按 Ctrl+C 停止监控"
    echo ""
    
    while true; do
        if ! check_server; then
            log_message "❌ 服务器异常，正在自动重启..."
            restart_server
            if [ $? -eq 0 ]; then
                log_message "✅ 服务器重启成功"
            else
                log_message "❌ 服务器重启失败，等待下次检查..."
            fi
        else
            # 静默运行，只在有问题时输出
            echo -n "."
        fi
        
        sleep $CHECK_INTERVAL
    done
}

# 信号处理
cleanup() {
    echo ""
    log_message "🧹 收到停止信号，正在清理..."
    stop_server
    log_message "👋 监控已停止"
    exit 0
}

trap cleanup SIGINT SIGTERM

# 主程序
case "${1:-monitor}" in
    "start")
        cleanup_port
        start_server
        ;;
    "stop")
        stop_server
        ;;
    "restart")
        restart_server
        ;;
    "status")
        if check_server; then
            log_message "✅ 服务器运行正常"
        else
            log_message "❌ 服务器未运行或异常"
        fi
        ;;
    "monitor")
        # 首次启动
        if ! check_server; then
            cleanup_port
            start_server
        else
            log_message "✅ 服务器已在运行"
        fi
        
        # 开始监控
        monitor_loop
        ;;
    *)
        echo "数据记账系统 - 自动监控脚本"
        echo ""
        echo "用法: $0 {start|stop|restart|status|monitor}"
        echo ""
        echo "命令说明:"
        echo "  start   - 启动服务器"
        echo "  stop    - 停止服务器"
        echo "  restart - 重启服务器"
        echo "  status  - 检查服务器状态"
        echo "  monitor - 启动自动监控模式（默认）"
        echo ""
        echo "自动监控模式："
        echo "  - 每${CHECK_INTERVAL}秒检查一次服务器状态"
        echo "  - 服务器掉线时自动重启"
        echo "  - 按 Ctrl+C 停止监控"
        echo "  - 日志保存在: $LOG_FILE"
        exit 1
        ;;
esac
