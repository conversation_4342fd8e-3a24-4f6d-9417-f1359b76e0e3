<?php
// 快速安装脚本

header('Content-Type: text/html; charset=utf-8');

$step = $_GET['step'] ?? 1;
$action = $_POST['action'] ?? '';

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记账系统安装向导</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .step {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover { opacity: 0.9; }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .warning { color: #faad14; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 记账系统安装向导</h1>
            <p>步骤 <?php echo $step; ?> / 3</p>
        </div>
        
        <div class="content">
            <?php if ($step == 1): ?>
                <h2>环境检查</h2>
                <div class="step">
                    <?php
                    $checks = [
                        'PHP版本' => version_compare(PHP_VERSION, '7.4.0', '>='),
                        'PDO扩展' => extension_loaded('pdo'),
                        'PDO MySQL' => extension_loaded('pdo_mysql'),
                        'JSON扩展' => extension_loaded('json'),
                        'mbstring扩展' => extension_loaded('mbstring'),
                    ];
                    
                    $allPassed = true;
                    foreach ($checks as $name => $passed) {
                        $class = $passed ? 'success' : 'error';
                        $icon = $passed ? '✓' : '✗';
                        echo "<p class='$class'>$icon $name</p>";
                        if (!$passed) $allPassed = false;
                    }
                    ?>
                </div>
                
                <?php if ($allPassed): ?>
                    <a href="?step=2" class="btn">下一步：数据库配置</a>
                <?php else: ?>
                    <p class="error">请先解决环境问题后再继续</p>
                <?php endif; ?>
                
            <?php elseif ($step == 2): ?>
                <h2>数据库配置</h2>
                
                <?php if ($action == 'test_db'): ?>
                    <div class="step">
                        <?php
                        try {
                            $host = $_POST['host'];
                            $dbname = $_POST['dbname'];
                            $username = $_POST['username'];
                            $password = $_POST['password'];
                            
                            $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
                            $pdo = new PDO($dsn, $username, $password, [
                                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                            ]);
                            
                            echo "<p class='success'>✓ 数据库连接成功</p>";
                            
                            // 执行数据库初始化
                            $sql = file_get_contents('backend/database/init.sql');
                            $pdo->exec($sql);
                            
                            echo "<p class='success'>✓ 数据库初始化完成</p>";
                            echo "<a href='?step=3' class='btn'>下一步：完成安装</a>";
                            
                        } catch (Exception $e) {
                            echo "<p class='error'>✗ 数据库连接失败: " . $e->getMessage() . "</p>";
                        }
                        ?>
                    </div>
                <?php endif; ?>
                
                <form method="post">
                    <input type="hidden" name="action" value="test_db">
                    <div class="form-group">
                        <label>数据库主机:</label>
                        <input type="text" name="host" value="127.0.0.1" required>
                    </div>
                    <div class="form-group">
                        <label>数据库名:</label>
                        <input type="text" name="dbname" value="shuju" required>
                    </div>
                    <div class="form-group">
                        <label>用户名:</label>
                        <input type="text" name="username" value="shuju" required>
                    </div>
                    <div class="form-group">
                        <label>密码:</label>
                        <input type="password" name="password" value="Abc112211" required>
                    </div>
                    <button type="submit" class="btn">测试连接并初始化数据库</button>
                </form>
                
            <?php elseif ($step == 3): ?>
                <h2>安装完成</h2>
                <div class="step">
                    <p class="success">✓ 系统安装完成！</p>
                    <p>默认管理员账户：</p>
                    <ul>
                        <li>用户名: admin</li>
                        <li>密码: password</li>
                    </ul>
                </div>
                
                <h3>下一步操作：</h3>
                <div class="step">
                    <p>1. 构建前端应用（在 frontend 目录执行）：</p>
                    <code>npm install && npm run build</code>
                    
                    <p>2. 配置定时任务（可选）：</p>
                    <code>0 0 1 * * php <?php echo __DIR__; ?>/backend/cron/monthly_reset.php</code>
                    
                    <p>3. 删除安装文件：</p>
                    <code>install.php, test.php</code>
                </div>
                
                <a href="test.php" class="btn">系统测试</a>
                <a href="welcome.html" class="btn">访问系统</a>
                
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
