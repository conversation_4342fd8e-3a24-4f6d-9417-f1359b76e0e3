RewriteEngine On

# 处理 API 请求
RewriteRule ^api/(.*)$ backend/api/index.php [QSA,L]

# 处理前端路由 (Vue Router History Mode)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/backend/
RewriteCond %{REQUEST_URI} !^/api/
# 如果前端构建文件存在，使用它；否则使用简化版
RewriteCond %{DOCUMENT_ROOT}/frontend/dist/index.html -f
RewriteRule ^(.*)$ frontend/dist/index.html [L]
RewriteCond %{DOCUMENT_ROOT}/frontend/dist/index.html !-f
RewriteRule ^(.*)$ simple_frontend.html [L]

# 设置缓存
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# 启用 Gzip 压缩 - 性能优化
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json

    # 排除已压缩的文件
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|zip|gz|bz2)$ no-gzip dont-vary
</IfModule>

# 性能优化 - 设置Cache-Control头
<IfModule mod_headers.c>
    # 静态资源长期缓存
    <FilesMatch "\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2)$">
        Header set Cache-Control "public, max-age=31536000"
    </FilesMatch>

    # HTML文件短期缓存
    <FilesMatch "\.(html|htm)$">
        Header set Cache-Control "public, max-age=3600"
    </FilesMatch>

    # PHP文件不缓存
    <FilesMatch "\.(php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires "0"
    </FilesMatch>

    # 安全头
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>

# 设置默认字符集
AddDefaultCharset UTF-8

# 启用ETag
FileETag MTime Size