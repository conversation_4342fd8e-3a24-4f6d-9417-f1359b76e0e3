<?php

// 设置时区为北京时间
date_default_timezone_set('Asia/Shanghai');

// 数据库配置
define('DB_HOST', 'shuju-mysql.ns-qqzrupud.svc');
define('DB_NAME', 'shuju');
define('DB_USER', 'root');
define('DB_PASS', 'gb5bjqq9');
define('DB_CHARSET', 'utf8mb4');

// JWT 配置
define('JWT_SECRET', 'your-secret-key-change-this-in-production');
define('JWT_ALGORITHM', 'HS256');

// 跨域配置
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 数据库连接类
class Database {
    private $pdo;

    public function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $this->pdo = new PDO($dsn, DB_USER, DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]);
        } catch (PDOException $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }

    public function getConnection() {
        return $this->pdo;
    }
}

// JWT 工具类
class JWT {
    public static function encode($payload) {
        $header = json_encode(['typ' => 'JWT', 'alg' => JWT_ALGORITHM]);
        $payload = json_encode($payload);

        $headerEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $payloadEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signature = hash_hmac('sha256', $headerEncoded . "." . $payloadEncoded, JWT_SECRET, true);
        $signatureEncoded = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        return $headerEncoded . "." . $payloadEncoded . "." . $signatureEncoded;
    }

    public static function decode($jwt) {
        $parts = explode('.', $jwt);
        if (count($parts) !== 3) {
            throw new Exception('Invalid JWT');
        }

        $header = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[0]));
        $payload = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[1]));
        $signature = base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[2]));

        $expectedSignature = hash_hmac('sha256', $parts[0] . "." . $parts[1], JWT_SECRET, true);

        if (!hash_equals($signature, $expectedSignature)) {
            throw new Exception('Invalid signature');
        }

        return json_decode($payload, true);
    }
}

// 响应工具类
class Response {
    public static function json($data, $status = 200) {
        http_response_code($status);
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit();
    }

    public static function error($message, $status = 400) {
        self::json(['error' => $message], $status);
    }

    public static function success($data = null, $message = 'Success') {
        $response = ['message' => $message];
        if ($data !== null) {
            $response['data'] = $data;
        }
        self::json($response);
    }
}

// 获取请求数据
function getRequestData() {
    $input = file_get_contents('php://input');
    return json_decode($input, true) ?: [];
}

// 验证 JWT Token - 增强版本，支持多种header格式
function verifyToken() {
    $headers = getallheaders();
    $authHeader = '';

    // 尝试不同的header名称格式
    foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
        if (isset($headers[$headerName])) {
            $authHeader = $headers[$headerName];
            break;
        }
    }

    // 如果还是没有找到，尝试从$_SERVER中获取
    if (empty($authHeader)) {
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    }

    if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        Response::error('未提供认证令牌', 401);
    }

    try {
        $payload = JWT::decode($matches[1]);
        return $payload;
    } catch (Exception $e) {
        Response::error('无效的认证令牌', 401);
    }
}
