<?php

require_once '../api/config.php';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    // 获取所有用户
    $stmt = $pdo->prepare("SELECT id FROM users");
    $stmt->execute();
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        resetUserRecords($pdo, $user['id']);
    }
    
    echo "月度重置完成 - " . date('Y-m-d H:i:s') . "\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

function resetUserRecords($pdo, $userId) {
    // 获取用户的所有账本
    $stmt = $pdo->prepare("SELECT id FROM account_books WHERE user_id = ?");
    $stmt->execute([$userId]);
    $books = $stmt->fetchAll();
    
    $bookIds = array_column($books, 'id');
    if (empty($bookIds)) {
        return;
    }
    
    $placeholders = str_repeat('?,', count($bookIds) - 1) . '?';
    
    // 锁定上个月的记录
    $lastMonth = date('Y-m', strtotime('-1 month'));
    $stmt = $pdo->prepare("
        UPDATE records SET is_locked = 1 
        WHERE account_book_id IN ($placeholders) 
        AND completed_month = ?
    ");
    $stmt->execute(array_merge($bookIds, [$lastMonth]));
    
    // 重置当月记录（取消勾选）
    $stmt = $pdo->prepare("
        UPDATE records SET 
            is_completed = 0, 
            completed_month = NULL 
        WHERE account_book_id IN ($placeholders) 
        AND is_completed = 1 
        AND is_locked = 0
    ");
    $stmt->execute($bookIds);
    
    echo "用户 {$userId} 的记录已重置\n";
}
