<?php

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['PATH_INFO'] ?? '';

try {
    $user = verifyToken();
    $db = new Database();
    $pdo = $db->getConnection();
    
    switch ($method) {
        case 'GET':
            if ($path === '/overview') {
                getOverviewStats($pdo, $user['user_id']);
            } elseif ($path === '/monthly') {
                getMonthlyStats($pdo, $user['user_id']);
            } elseif ($path === '/trends') {
                getTrendStats($pdo, $user['user_id']);
            } elseif (preg_match('/^\/book\/(\d+)$/', $path, $matches)) {
                getBookStats($pdo, $user['user_id'], $matches[1]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        default:
            Response::error('不支持的请求方法', 405);
    }
} catch (Exception $e) {
    Response::error($e->getMessage(), 500);
}

function getOverviewStats($pdo, $userId) {
    // 总体统计
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(DISTINCT ab.id) as total_books,
            COUNT(r.id) as total_records,
            COUNT(CASE WHEN r.is_completed = 1 THEN 1 END) as completed_records,
            COALESCE(SUM(r.accumulated_amount), 0) as total_accumulated,
            COALESCE(SUM(CASE WHEN r.is_completed = 1 THEN r.monthly_amount ELSE 0 END), 0) as monthly_income
        FROM account_books ab
        LEFT JOIN records r ON ab.id = r.account_book_id
        WHERE ab.user_id = ?
    ");
    $stmt->execute([$userId]);
    $overview = $stmt->fetch();
    
    // 本月统计
    $currentMonth = date('Y-m');
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(r.id) as month_records,
            COUNT(CASE WHEN r.is_completed = 1 AND r.completed_month = ? THEN 1 END) as month_completed,
            COALESCE(SUM(CASE WHEN r.is_completed = 1 AND r.completed_month = ? THEN r.monthly_amount ELSE 0 END), 0) as month_income
        FROM account_books ab
        LEFT JOIN records r ON ab.id = r.account_book_id
        WHERE ab.user_id = ?
    ");
    $stmt->execute([$currentMonth, $currentMonth, $userId]);
    $monthStats = $stmt->fetch();
    
    // 续期时间分布
    $stmt = $pdo->prepare("
        SELECT 
            renewal_time,
            COUNT(*) as count,
            ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM records r2 JOIN account_books ab2 ON r2.account_book_id = ab2.id WHERE ab2.user_id = ?), 2) as percentage
        FROM records r
        JOIN account_books ab ON r.account_book_id = ab.id
        WHERE ab.user_id = ?
        GROUP BY renewal_time
        ORDER BY count DESC
    ");
    $stmt->execute([$userId, $userId]);
    $renewalDistribution = $stmt->fetchAll();
    
    Response::success([
        'overview' => $overview,
        'current_month' => $monthStats,
        'renewal_distribution' => $renewalDistribution
    ]);
}

function getMonthlyStats($pdo, $userId) {
    // 获取最近12个月的统计
    $months = [];
    for ($i = 11; $i >= 0; $i--) {
        $months[] = date('Y-m', strtotime("-$i months"));
    }
    
    $monthlyData = [];
    foreach ($months as $month) {
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(r.id) as total_records,
                COUNT(CASE WHEN r.is_completed = 1 AND r.completed_month = ? THEN 1 END) as completed_records,
                COALESCE(SUM(CASE WHEN r.is_completed = 1 AND r.completed_month = ? THEN r.monthly_amount ELSE 0 END), 0) as income,
                COALESCE(SUM(CASE WHEN r.completed_month = ? THEN r.accumulated_amount ELSE 0 END), 0) as accumulated
            FROM account_books ab
            LEFT JOIN records r ON ab.id = r.account_book_id
            WHERE ab.user_id = ?
        ");
        $stmt->execute([$month, $month, $month, $userId]);
        $data = $stmt->fetch();
        
        $monthlyData[] = [
            'month' => $month,
            'month_name' => date('Y年m月', strtotime($month . '-01')),
            'total_records' => (int)$data['total_records'],
            'completed_records' => (int)$data['completed_records'],
            'completion_rate' => $data['total_records'] > 0 ? round(($data['completed_records'] / $data['total_records']) * 100, 2) : 0,
            'income' => (float)$data['income'],
            'accumulated' => (float)$data['accumulated']
        ];
    }
    
    Response::success($monthlyData);
}

function getTrendStats($pdo, $userId) {
    // 完成率趋势
    $stmt = $pdo->prepare("
        SELECT 
            DATE(r.updated_at) as date,
            COUNT(r.id) as total_records,
            COUNT(CASE WHEN r.is_completed = 1 THEN 1 END) as completed_records,
            COALESCE(SUM(CASE WHEN r.is_completed = 1 THEN r.monthly_amount ELSE 0 END), 0) as daily_income
        FROM account_books ab
        LEFT JOIN records r ON ab.id = r.account_book_id
        WHERE ab.user_id = ? 
        AND r.updated_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(r.updated_at)
        ORDER BY date DESC
        LIMIT 30
    ");
    $stmt->execute([$userId]);
    $dailyTrends = $stmt->fetchAll();
    
    // 处理数据
    $trends = array_map(function($item) {
        return [
            'date' => $item['date'],
            'total_records' => (int)$item['total_records'],
            'completed_records' => (int)$item['completed_records'],
            'completion_rate' => $item['total_records'] > 0 ? round(($item['completed_records'] / $item['total_records']) * 100, 2) : 0,
            'daily_income' => (float)$item['daily_income']
        ];
    }, $dailyTrends);
    
    // 账本活跃度
    $stmt = $pdo->prepare("
        SELECT 
            ab.name as book_name,
            COUNT(r.id) as record_count,
            COUNT(CASE WHEN r.is_completed = 1 THEN 1 END) as completed_count,
            COALESCE(SUM(r.accumulated_amount), 0) as total_accumulated,
            MAX(r.updated_at) as last_activity
        FROM account_books ab
        LEFT JOIN records r ON ab.id = r.account_book_id
        WHERE ab.user_id = ?
        GROUP BY ab.id, ab.name
        ORDER BY record_count DESC
    ");
    $stmt->execute([$userId]);
    $bookActivity = $stmt->fetchAll();
    
    Response::success([
        'daily_trends' => array_reverse($trends), // 按时间正序
        'book_activity' => $bookActivity
    ]);
}

function getBookStats($pdo, $userId, $bookId) {
    // 验证账本所有权
    $stmt = $pdo->prepare("SELECT name FROM account_books WHERE id = ? AND user_id = ?");
    $stmt->execute([$bookId, $userId]);
    $book = $stmt->fetch();
    
    if (!$book) {
        Response::error('账本不存在或无权限', 404);
    }
    
    // 账本基础统计
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completed_records,
            COUNT(CASE WHEN is_locked = 1 THEN 1 END) as locked_records,
            COALESCE(SUM(accumulated_amount), 0) as total_accumulated,
            COALESCE(AVG(amount), 0) as avg_amount,
            COALESCE(MAX(amount), 0) as max_amount,
            COALESCE(MIN(amount), 0) as min_amount
        FROM records 
        WHERE account_book_id = ?
    ");
    $stmt->execute([$bookId]);
    $basicStats = $stmt->fetch();
    
    // 按姓名统计
    $stmt = $pdo->prepare("
        SELECT 
            name,
            COUNT(*) as record_count,
            COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completed_count,
            COALESCE(SUM(accumulated_amount), 0) as total_accumulated,
            COALESCE(SUM(amount), 0) as total_amount
        FROM records 
        WHERE account_book_id = ?
        GROUP BY name
        ORDER BY total_accumulated DESC
    ");
    $stmt->execute([$bookId]);
    $nameStats = $stmt->fetchAll();
    
    // 按续期时间统计
    $stmt = $pdo->prepare("
        SELECT 
            renewal_time,
            COUNT(*) as count,
            COALESCE(SUM(accumulated_amount), 0) as total_accumulated,
            COALESCE(AVG(amount), 0) as avg_amount
        FROM records 
        WHERE account_book_id = ?
        GROUP BY renewal_time
        ORDER BY count DESC
    ");
    $stmt->execute([$bookId]);
    $renewalStats = $stmt->fetchAll();
    
    // 最近活动
    $stmt = $pdo->prepare("
        SELECT 
            name,
            amount,
            monthly_amount,
            is_completed,
            updated_at
        FROM records 
        WHERE account_book_id = ?
        ORDER BY updated_at DESC
        LIMIT 10
    ");
    $stmt->execute([$bookId]);
    $recentActivity = $stmt->fetchAll();
    
    Response::success([
        'book_name' => $book['name'],
        'basic_stats' => $basicStats,
        'name_stats' => $nameStats,
        'renewal_stats' => $renewalStats,
        'recent_activity' => $recentActivity
    ]);
}
?>
