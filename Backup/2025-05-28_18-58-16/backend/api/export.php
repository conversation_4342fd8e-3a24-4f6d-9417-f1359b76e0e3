<?php

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['PATH_INFO'] ?? '';

try {
    // 支持URL参数中的token
    $token = $_GET['token'] ?? '';
    if ($token) {
        $_SERVER['HTTP_AUTHORIZATION'] = 'Bearer ' . $token;
    }

    $user = verifyToken();
    $db = new Database();
    $pdo = $db->getConnection();

    switch ($method) {
        case 'GET':
            if ($path === '/csv') {
                exportToCSV($pdo, $user['user_id']);
            } elseif ($path === '/json') {
                exportToJSON($pdo, $user['user_id']);
            } elseif (preg_match('/^\/book\/(\d+)\/csv$/', $path, $matches)) {
                exportBookToCSV($pdo, $user['user_id'], $matches[1]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;

        default:
            Response::error('不支持的请求方法', 405);
    }
} catch (Exception $e) {
    Response::error($e->getMessage(), 500);
}

function exportToCSV($pdo, $userId) {
    // 获取用户所有数据
    $stmt = $pdo->prepare("
        SELECT
            ab.name as book_name,
            r.date,
            r.name,
            r.amount,
            r.monthly_amount,
            r.renewal_time,
            r.renewal_amount,
            r.remark,
            r.accumulated_amount,
            r.is_completed,
            r.completed_month,
            r.is_locked,
            r.created_at,
            r.updated_at
        FROM account_books ab
        LEFT JOIN records r ON ab.id = r.account_book_id
        WHERE ab.user_id = ?
        ORDER BY ab.name, r.date DESC
    ");
    $stmt->execute([$userId]);
    $records = $stmt->fetchAll();

    // 设置CSV头部
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="accounting_data_' . date('Y-m-d') . '.csv"');

    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";

    $output = fopen('php://output', 'w');

    // CSV标题行
    $headers = [
        '账本名称', '日期', '姓名', '金额', '每月金额', '续期时间',
        '续期金额', '备注', '累计金额', '是否完成', '完成月份',
        '是否锁定', '创建时间', '更新时间'
    ];
    fputcsv($output, $headers);

    // 数据行
    foreach ($records as $record) {
        if ($record['date']) { // 只导出有记录的数据
            $row = [
                $record['book_name'],
                $record['date'],
                $record['name'],
                $record['amount'],
                $record['monthly_amount'],
                $record['renewal_time'],
                $record['renewal_amount'],
                $record['remark'],
                $record['accumulated_amount'],
                $record['is_completed'] ? '是' : '否',
                $record['completed_month'] ?: '',
                $record['is_locked'] ? '是' : '否',
                $record['created_at'],
                $record['updated_at']
            ];
            fputcsv($output, $row);
        }
    }

    fclose($output);
    exit;
}

function exportBookToCSV($pdo, $userId, $bookId) {
    // 验证账本所有权
    $stmt = $pdo->prepare("SELECT name FROM account_books WHERE id = ? AND user_id = ?");
    $stmt->execute([$bookId, $userId]);
    $book = $stmt->fetch();

    if (!$book) {
        Response::error('账本不存在或无权限', 404);
    }

    // 获取账本记录
    $stmt = $pdo->prepare("
        SELECT
            date,
            name,
            amount,
            monthly_amount,
            renewal_time,
            renewal_amount,
            remark,
            accumulated_amount,
            is_completed,
            completed_month,
            is_locked,
            created_at,
            updated_at
        FROM records
        WHERE account_book_id = ?
        ORDER BY date DESC
    ");
    $stmt->execute([$bookId]);
    $records = $stmt->fetchAll();

    // 设置CSV头部
    $filename = 'book_' . preg_replace('/[^a-zA-Z0-9_-]/', '_', $book['name']) . '_' . date('Y-m-d') . '.csv';
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";

    $output = fopen('php://output', 'w');

    // CSV标题行
    $headers = [
        '日期', '姓名', '金额', '每月金额', '续期时间',
        '续期金额', '备注', '累计金额', '是否完成', '完成月份',
        '是否锁定', '创建时间', '更新时间'
    ];
    fputcsv($output, $headers);

    // 数据行
    foreach ($records as $record) {
        $row = [
            $record['date'],
            $record['name'],
            $record['amount'],
            $record['monthly_amount'],
            $record['renewal_time'],
            $record['renewal_amount'],
            $record['remark'],
            $record['accumulated_amount'],
            $record['is_completed'] ? '是' : '否',
            $record['completed_month'] ?: '',
            $record['is_locked'] ? '是' : '否',
            $record['created_at'],
            $record['updated_at']
        ];
        fputcsv($output, $row);
    }

    fclose($output);
    exit;
}

function exportToJSON($pdo, $userId) {
    // 获取用户信息
    $stmt = $pdo->prepare("SELECT username, email, created_at FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();

    // 获取账本信息
    $stmt = $pdo->prepare("
        SELECT
            id,
            name,
            description,
            created_at,
            updated_at
        FROM account_books
        WHERE user_id = ?
        ORDER BY created_at
    ");
    $stmt->execute([$userId]);
    $books = $stmt->fetchAll();

    // 获取每个账本的记录
    foreach ($books as &$book) {
        $stmt = $pdo->prepare("
            SELECT
                id,
                date,
                name,
                amount,
                monthly_amount,
                renewal_time,
                renewal_amount,
                remark,
                accumulated_amount,
                is_completed,
                completed_month,
                is_locked,
                created_at,
                updated_at
            FROM records
            WHERE account_book_id = ?
            ORDER BY date DESC
        ");
        $stmt->execute([$book['id']]);
        $book['records'] = $stmt->fetchAll();
    }

    // 构建导出数据
    $exportData = [
        'export_info' => [
            'exported_at' => date('Y-m-d H:i:s'),
            'version' => '1.0',
            'format' => 'json'
        ],
        'user_info' => $user,
        'account_books' => $books
    ];

    // 设置JSON头部
    $filename = 'accounting_data_' . date('Y-m-d') . '.json';
    header('Content-Type: application/json; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    echo json_encode($exportData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}
?>
