<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记账管理系统</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">

    <style>
        /* CSS变量 - 统一管理颜色和尺寸 */
        :root {
            --primary-color: #667eea;
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-color: #228B22;
            --error-color: #ff4d4f;
            --warning-color: #ff6b35;
            --border-radius: 12px;
            --border-radius-small: 4px;
            --transition: all 0.3s ease;
            --shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            --shadow-hover: 0 4px 16px rgba(0, 0, 0, 0.12);
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: var(--primary-gradient);
            min-height: 100vh;
            overflow-x: hidden;
            /* 优化字体渲染 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }
        .app-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 40px;
            max-width: 400px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .logo {
            text-align: center;
            font-size: 48px;
            margin-bottom: 20px;
        }
        .title {
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: opacity 0.3s;
        }
        .btn:hover {
            opacity: 0.9;
        }
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .error {
            color: #ff4d4f;
            margin-top: 10px;
            text-align: center;
        }
        .success {
            color: #52c41a;
            margin-top: 10px;
            text-align: center;
        }
        .link {
            text-align: center;
            margin-top: 20px;
        }
        .link a {
            color: #667eea;
            text-decoration: none;
        }
        .main-app {
            width: 100%;
            max-width: 800px;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 0 auto;
        }

        /* 移动端全屏布局 */
        @media (max-width: 768px) {
            .app-container {
                padding: 0;
                align-items: stretch;
            }
            .main-app {
                width: 100%;
                height: 100vh;
                border-radius: 0;
                display: flex;
                flex-direction: column;
            }
        }

        /* 电脑端居中显示 */
        @media (min-width: 769px) {
            .main-app {
                margin: 20px auto;
                max-width: 900px;
            }
            /* 只对主应用页面的app-container应用flex-start */
            #main-page.app-container {
                display: flex;
                justify-content: center;
                align-items: flex-start;
                min-height: 100vh;
                padding: 20px;
            }
            /* 登录页面保持居中 */
            #login-page.app-container {
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
                padding: 20px;
            }
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
        }
        .content {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }
        .record-card {
            border: 1px solid #e8e8e8;
            border-radius: var(--border-radius);
            padding: 16px;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            box-shadow: var(--shadow);
            transition: var(--transition);
            position: relative;
            cursor: pointer;
            user-select: none;
            /* 渲染优化 */
            contain: layout style paint;
            backface-visibility: hidden;
            perspective: 1000px;
        }

        /* 已完成记录的样式 */
        .record-card.completed {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-color: #0ea5e9;
            box-shadow: 0 2px 12px rgba(14, 165, 233, 0.15);
        }

        /* 状态徽章样式 */
        .status-badge.renewal {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            white-space: nowrap;
            margin: 0 12px;
            box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);
            vertical-align: middle;
            display: inline-block;
        }

        /* 已完成记录的样式 */
        .record-card.completed {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-color: #0ea5e9;
            box-shadow: 0 2px 12px rgba(14, 165, 233, 0.15);
        }

        /* 续期记录的样式 - 未完成时只有背景色，完成后保持黄色背景+边框 */
        .record-card.renewal-month {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            box-shadow: 0 2px 12px rgba(245, 158, 11, 0.15);
        }

        /* 续期记录完成后的边框样式 - 保持黄色背景 */
        .record-card.renewal-month.completed {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-color: #f59e0b;
            box-shadow: 0 2px 12px rgba(245, 158, 11, 0.15);
        }
        .record-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
            border-color: #d9d9d9;
        }
        .record-header {
            display: flex;
            align-items: center;
            margin-bottom: 0px;
            gap: 15px;
        }
        .record-header .left-section {
            display: flex;
            align-items: center;
            flex: 1;
            gap: 15px;
        }
        .record-header .right-section {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .record-name {
            font-size: 20px;
            font-weight: 700;
            color: #1a1a1a;
            margin-right: 15px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        .record-amount {
            font-size: 18px;
            font-weight: 700;
            color: #ff4757;
            background: linear-gradient(135deg, #ff6b7a, #ff4757);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .record-details {
            display: none;
        }
        .record-details-inline {
            display: flex;
            gap: 12px;
            font-size: 13px;
            color: #555;
            line-height: 1.4;
            font-weight: 500;
        }
        .record-details > div,
        .record-details-inline > div {
            white-space: nowrap;
            background: rgba(24, 144, 255, 0.06);
            padding: 3px 6px;
            border-radius: 4px;
            border: 1px solid rgba(24, 144, 255, 0.12);
            font-size: 12px;
        }
        .record-details .accumulated,
        .record-details-inline .accumulated,
        .record-header .accumulated {
            background: rgba(82, 196, 26, 0.08);
            border-color: rgba(82, 196, 26, 0.15);
            color: #52c41a;
            font-weight: 600;
        }
        .floating-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: var(--primary-gradient);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
            /* 增强触摸体验 */
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
            transition: var(--transition);
            transform: translateZ(0);
            /* 渲染优化 */
            backface-visibility: hidden;
            perspective: 1000px;
        }
        .floating-btn:active {
            transform: scale(0.9) translateZ(0);
            transition: transform 0.1s ease;
        }

        /* 滚动回弹效果 - 全局生效 */
        .content.bounce-top {
            animation: bounceTop 0.3s ease-out;
        }

        .content.bounce-bottom {
            animation: bounceBottom 0.3s ease-out;
        }

        @keyframes bounceTop {
            0% { transform: translateY(0); }
            50% { transform: translateY(12px); }
            100% { transform: translateY(0); }
        }

        @keyframes bounceBottom {
            0% { transform: translateY(0); }
            50% { transform: translateY(-12px); }
            100% { transform: translateY(0); }
        }

        /* 桌面端专用样式 */
        @media (min-width: 769px) {
            .record-card {
                padding: 12px 16px !important;
                margin-bottom: 8px !important;
                display: flex !important;
                align-items: center !important;
                min-height: 48px !important;
            }
            .record-header {
                align-items: center !important;
                margin-bottom: 0 !important;
                width: 100% !important;
                height: auto !important;
            }
            .record-header .left-section {
                align-items: center !important;
            }
            .record-header .right-section {
                align-items: center !important;
            }
            .record-header button {
                padding: 6px 12px !important;
                font-size: 12px !important;
                border-radius: 6px !important;
                font-weight: 500 !important;
                transition: all 0.2s ease !important;
                width: auto !important;
                height: auto !important;
            }
            .record-name {
                font-size: 20px !important;
                font-weight: 700 !important;
                line-height: 1.2 !important;
            }
            .record-amount {
                font-size: 18px !important;
                font-weight: 700 !important;
                line-height: 1.2 !important;
            }
            .record-details-inline {
                align-items: center !important;
            }
            .record-details-inline > div {
                line-height: 1.2 !important;
                display: flex !important;
                align-items: center !important;
            }
            .status-badge.renewal {
                line-height: 1 !important;
                display: inline-flex !important;
                align-items: center !important;
            }
        }
        /* 手机端优化 */
        @media (max-width: 768px) {
            /* 全局触摸优化 */
            * {
                -webkit-tap-highlight-color: transparent;
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                user-select: none;
            }

            /* 允许文本选择的元素 */
            input, textarea, .record-name, .record-amount {
                -webkit-user-select: text;
                user-select: text;
            }

            /* 整体容器滚动优化 */
            .app-container {
                -webkit-overflow-scrolling: touch;
                overscroll-behavior: contain;
            }
            .record-card {
                padding: 8px;
                margin-bottom: 6px;
                position: relative;
                /* 增强触摸体验 */
                touch-action: manipulation;
                -webkit-tap-highlight-color: transparent;
                transition: transform 0.2s ease, box-shadow 0.2s ease;
                transform: translateZ(0);
                backface-visibility: hidden;
            }



            /* 移动端续期徽章样式 - 跨越边线效果 */
            .status-badge.renewal {
                position: absolute !important;
                top: -8px !important;
                right: 20px !important;
                margin: 0 !important;
                font-size: 9px !important;
                padding: 4px 8px !important;
                border-radius: 8px !important;
                z-index: 10 !important;
                border: 2px solid white !important;
            }
            .record-card:active {
                transform: scale(0.98) translateZ(0);
                transition: transform 0.1s ease;
            }
            .record-header {
                margin-bottom: 4px;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                gap: 6px;
                min-height: 32px;
            }
            .record-header > div:first-child {
                display: flex;
                align-items: center;
                flex: 1;
                min-width: 0;
                gap: 12px;
                padding-right: 60px;
            }
            .record-header > div:last-child {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 2px;
                flex-shrink: 0;
                position: absolute;
                right: 8px;
                top: 50%;
                transform: translateY(-50%);
            }
            /* 移动端文字大小统一 */
            .record-name {
                font-size: 14px !important;
                font-weight: 600 !important;
                margin-right: 12px;
                white-space: nowrap;
            }
            .record-amount {
                font-size: 14px !important;
                font-weight: 700 !important;
                margin-right: 12px;
                white-space: nowrap;
            }
            /* 移动端累计金额样式优化 - 深绿色 */
            .record-header .accumulated {
                font-size: 11px;
                padding: 2px 5px;
                margin-left: 8px;
                border-radius: 3px;
                background: rgba(34, 139, 34, 0.15);
                border: 1px solid rgba(34, 139, 34, 0.3);
                color: #228B22;
                font-weight: 600;
                white-space: nowrap;
            }
            /* 移动端小图标按钮 */
            .record-header button {
                width: 22px !important;
                height: 22px !important;
                padding: 0 !important;
                border-radius: 3px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                font-size: 11px !important;
                font-weight: normal !important;
                border: 1px solid !important;
                min-width: 22px !important;
                /* 增强触摸体验 */
                touch-action: manipulation;
                -webkit-tap-highlight-color: transparent;
                transition: all 0.2s ease;
                transform: translateZ(0);
            }
            .record-header button:active {
                transform: scale(0.9) translateZ(0);
                transition: transform 0.1s ease;
            }
            .record-details-inline {
                display: none !important;
            }
            .record-details {
                display: flex !important;
                flex-wrap: wrap;
                gap: 12px;
                font-size: 12px;
                line-height: 1.3;
                margin-top: 4px;
            }
            .record-details > div {
                white-space: nowrap;
                padding: 2px 5px !important;
                font-size: 11px !important;
                border-radius: 3px !important;
                margin-right: 6px !important;
            }
            /* 日期特殊样式 */
            .record-details .date-highlight {
                font-weight: 700 !important;
                color: #1890ff !important;
                background: rgba(24, 144, 255, 0.1) !important;
                border: 1px solid rgba(24, 144, 255, 0.2) !important;
                padding: 3px 6px !important;
            }
            .content {
                padding: 12px;
                flex: 1;
                overflow-y: auto;
                -webkit-overflow-scrolling: touch;
                /* 增强滚动体验 */
                overscroll-behavior-y: contain;
                scroll-behavior: smooth;
                /* iOS回弹效果 */
                -webkit-overflow-scrolling: touch;
                /* 防止滚动卡顿 */
                transform: translateZ(0);
                will-change: scroll-position;
            }
            .header {
                padding: 12px;
                flex-shrink: 0;
            }
            /* 手机端统计表头优化 */
            #statistics-header {
                padding: 6px !important;
                margin-bottom: 8px !important;
            }
            #statistics-header > div {
                grid-template-columns: repeat(2, 1fr) !important;
                gap: 4px !important;
            }
            #statistics-header > div > div {
                padding: 4px !important;
            }
            #statistics-header div > div:first-child {
                font-size: 9px !important;
                margin-bottom: 1px !important;
            }
            #statistics-header div > div:last-child {
                font-size: 12px !important;
            }
            /* 手机端记录列表头部优化 - 紧凑单行布局 */
            .records-header {
                margin-bottom: 12px !important;
            }

            .header-controls {
                gap: 6px !important;
                margin-bottom: 0 !important;
            }

            .custom-select {
                font-size: 12px !important;
                padding: 6px 8px !important;
                max-width: 110px !important;
                flex: 0 1 auto !important;
                border: 1px solid #ddd !important;
                border-radius: 4px !important;
                background: #f0f0f0 !important;
                height: auto !important;
                cursor: pointer !important;
                min-height: 32px !important;
                box-sizing: border-box !important;
                /* 增强触摸体验 */
                touch-action: manipulation;
                -webkit-tap-highlight-color: transparent;
                transition: all 0.2s ease;
                transform: translateZ(0);
            }
            .custom-select:active {
                transform: scale(0.98) translateZ(0);
                transition: transform 0.1s ease;
            }

            .custom-select #selected-book-text {
                font-size: 12px !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                flex: 1 !important;
            }

            #book-options {
                margin-top: 2px !important;
                border-radius: 4px !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
                z-index: 9999 !important;
                position: absolute !important;
                background: white !important;
                border: 1px solid #ddd !important;
            }

            #book-options > div {
                font-size: 12px !important;
                padding: 8px 12px !important;
                border-bottom: 1px solid #f0f0f0 !important;
                /* 增强触摸体验 */
                touch-action: manipulation;
                -webkit-tap-highlight-color: transparent;
                transition: all 0.2s ease;
                transform: translateZ(0);
            }
            #book-options > div:active {
                transform: scale(0.98) translateZ(0);
                transition: transform 0.1s ease;
            }

            #book-options > div:last-child {
                border-bottom: none !important;
            }

            .header-controls button {
                font-size: 12px !important;
                padding: 6px 12px !important;
                white-space: nowrap !important;
                height: auto !important;
                min-height: 32px !important;
                /* 增强触摸体验 */
                touch-action: manipulation;
                -webkit-tap-highlight-color: transparent;
                transition: all 0.2s ease;
                transform: translateZ(0);
            }
            .header-controls button:active {
                transform: scale(0.95) translateZ(0);
                transition: transform 0.1s ease;
            }

            .header-stats {
                margin-top: 0 !important;
            }

            #records-count {
                font-size: 12px !important;
                white-space: nowrap !important;
                flex-shrink: 0 !important;
            }

            /* 记录容器滚动优化 */
            #records-container {
                -webkit-overflow-scrolling: touch;
                overscroll-behavior-y: contain;
                scroll-behavior: smooth;
                transform: translateZ(0);
                will-change: scroll-position;
                /* 添加底部padding，避免被浮动按钮遮挡 */
                padding-bottom: 80px;
            }

            /* 浮动按钮在手机端的优化 */
            .floating-btn {
                bottom: 16px;
                right: 16px;
                width: 52px;
                height: 52px;
                font-size: 22px;
                box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
            }
        }

        /* 桌面端样式 - 使用更高的特异性避免!important */
        @media (min-width: 769px) {
            body.desktop-mode .record-header .accumulated {
                font-size: 13px;
                padding: 4px 8px;
                border-radius: 4px;
                background: rgba(34, 139, 34, 0.15);
                border: 1px solid rgba(34, 139, 34, 0.3);
                color: #228B22;
                font-weight: 600;
                white-space: nowrap;
            }

            body.desktop-mode .floating-btn {
                bottom: 20px;
                right: 20px;
                width: 56px;
                height: 56px;
                font-size: 24px;
            }
            .container {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }

            .header-controls {
                font-size: 14px !important;
                gap: 8px !important;
            }

            .header-controls button {
                font-size: 14px !important;
                padding: 8px 16px !important;
            }

            .custom-select {
                font-size: 14px !important;
                padding: 8px 12px !important;
                max-width: 200px !important;
                min-width: 150px !important;
            }

            .custom-select #selected-book-text {
                font-size: 14px !important;
            }

            #book-options {
                margin-top: 2px !important;
                border-radius: 4px !important;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
                z-index: 9999 !important;
                position: absolute !important;
                background: white !important;
                border: 1px solid #ddd !important;
            }

            #book-options > div {
                font-size: 14px !important;
                padding: 10px 12px !important;
                border-bottom: 1px solid #f0f0f0 !important;
            }

            .records-stats {
                font-size: 14px !important;
            }

            .record-item {
                padding: 16px !important;
                font-size: 14px !important;
            }

            .record-main {
                font-size: 16px !important;
            }

            .record-details {
                font-size: 13px !important;
            }

            .record-actions button {
                font-size: 13px !important;
                padding: 6px 12px !important;
            }

            .form-group label {
                font-size: 14px !important;
            }

            .form-group input, .form-group select, .form-group textarea {
                font-size: 14px !important;
                padding: 10px !important;
            }

            .btn {
                font-size: 14px !important;
                padding: 10px 20px !important;
            }

            .btn-small {
                font-size: 12px !important;
                padding: 6px 12px !important;
            }

            /* 桌面端记录容器不需要底部padding */
            #records-container {
                padding-bottom: 0 !important;
            }

            /* 桌面端记录网格布局 */
            .record-header .left-section {
                display: grid !important;
                grid-template-columns: 40px 120px 100px 100px 1fr !important;
                gap: 16px !important;
                align-items: center !important;
                flex: 1 !important;
                max-width: calc(100% - 120px) !important;
            }

            /* 桌面端显示详细信息 */
            .record-details-inline {
                display: flex !important;
                gap: 12px !important;
                align-items: center !important;
                font-size: 13px !important;
                color: #666 !important;
                flex-wrap: wrap !important;
            }

            .record-details-inline span {
                white-space: nowrap !important;
                flex-shrink: 0 !important;
            }

            /* 桌面端日期高亮样式 */
            .record-details-inline span:first-child {
                font-weight: 700;
                color: #1890ff;
                background: rgba(24, 144, 255, 0.1);
                padding: 2px 6px;
                border-radius: 4px;
                border: 1px solid rgba(24, 144, 255, 0.2);
            }

            /* 桌面端隐藏移动端的详细信息 */
            .record-details {
                display: none;
            }

            /* 桌面端累计金额样式 - 深绿色 */
            .record-header .accumulated {
                font-size: 13px;
                padding: 4px 8px;
                border-radius: 4px;
                background: rgba(34, 139, 34, 0.15);
                border: 1px solid rgba(34, 139, 34, 0.3);
                color: #228B22;
                font-weight: 600;
                white-space: nowrap;
            }

            /* 桌面端记录头部布局 */
            .record-header {
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
                width: 100% !important;
                padding: 0 !important;
            }

            /* 桌面端右侧按钮区域 */
            .record-header .right-section {
                display: flex !important;
                gap: 8px !important;
                flex-shrink: 0 !important;
                width: 100px !important;
                justify-content: flex-end !important;
            }

            /* 桌面端头部月份标识优化 - 与管理按钮高度一致 */
            #header-month-indicator {
                padding: 8px 16px !important;
                font-size: 14px !important;
                border-radius: 4px !important;
                min-width: 120px !important;
                text-align: center !important;
                height: auto !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                box-sizing: border-box !important;
                font-weight: 700 !important;
            }

            /* 桌面端月份导航栏优化 */
            #month-navigation button {
                padding: 8px 16px;
                font-size: 12px;
                border-radius: 20px;
                white-space: nowrap;
                min-width: 60px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            #current-month-display {
                padding: 8px 16px;
                font-size: 13px;
                min-width: 100px;
                border-radius: 20px;
                white-space: nowrap;
                display: flex;
                align-items: center;
                justify-content: center;
            }

        }

        /* 累计收益输入框样式增强 */
        .accumulated-amount-container .amount-input::placeholder {
            color: rgba(44, 62, 80, 0.6);
            font-weight: 700;
        }

        .accumulated-amount-container .amount-input:focus {
            background: rgba(255, 255, 255, 0.2) !important;
            border-radius: 10px;
            padding: 6px 12px;
            transition: all 0.3s ease;
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.4);
        }

        .accumulated-amount-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(127, 179, 211, 0.4);
            transition: all 0.3s ease;
        }

        .accumulated-amount-container:hover .amount-icon {
            background: rgba(255, 255, 255, 0.35);
            transform: scale(1.05);
            transition: all 0.3s ease;
        }

        /* 移动端响应式样式 */
        @media (max-width: 768px) {
            .accumulated-amount-container {
                padding: 14px !important;
                border-radius: 14px !important;
            }

            .accumulated-amount-container .amount-icon {
                width: 36px !important;
                height: 36px !important;
                font-size: 16px !important;
            }

            .accumulated-amount-container .accumulated-amount-display {
                gap: 12px !important;
            }

            .accumulated-amount-container .accumulated-amount-display > div:first-child {
                gap: 8px !important;
            }

            .accumulated-amount-container .accumulated-amount-display > div:first-child > div:last-child {
                font-size: 14px !important;
            }

            .accumulated-amount-container .amount-input {
                font-size: 20px !important;
                min-width: 80px !important;
            }
        }

        @media (max-width: 480px) {
            .accumulated-amount-container {
                padding: 12px !important;
                border-radius: 12px !important;
            }

            .accumulated-amount-container .amount-icon {
                width: 32px !important;
                height: 32px !important;
                font-size: 14px !important;
            }

            .accumulated-amount-container .accumulated-amount-display > div:first-child > div:last-child {
                font-size: 13px !important;
            }

            .accumulated-amount-container .amount-input {
                font-size: 18px !important;
                min-width: 70px !important;
            }

            /* 移动端续期时间显示优化 */
            #next-renewal-date {
                font-size: 13px !important;
            }

            .form-group > div > div > div:last-child {
                font-size: 11px !important;
                color: #666 !important;
            }
        }

        @media (max-width: 480px) {
            /* 手机端续期时间显示优化 - 保持同一行 */
            #next-renewal-date {
                font-size: 12px !important;
            }

            .form-group > div > div > div:last-child {
                font-size: 10px !important;
                color: #666 !important;
            }

            /* 确保手机端也保持同一行显示 */
            .form-group > div:first-child {
                flex-wrap: nowrap !important;
                gap: 4px !important;
            }

            /* 如果空间不够，优先缩小间距而不是换行 */
            .form-group > div:first-child > div:last-child {
                gap: 3px !important;
            }

            /* 移动端月份导航栏优化 */
            #month-navigation {
                bottom: 10px !important;
                padding: 6px 8px !important;
                border-radius: 20px !important;
            }

            #month-navigation > div {
                gap: 6px !important;
            }

            #month-navigation button {
                padding: 6px 8px !important;
                font-size: 10px !important;
                border-radius: 16px !important;
                white-space: nowrap !important;
                min-width: 45px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            #current-month-display {
                padding: 6px 8px !important;
                font-size: 10px !important;
                min-width: 70px !important;
                border-radius: 16px !important;
                white-space: nowrap !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            /* 移动端头部月份标识优化 - 与管理按钮高度一致 */
            #header-month-indicator {
                padding: 6px 8px !important;
                font-size: 11px !important;
                border-radius: 4px !important;
                min-width: 65px !important;
                text-align: center !important;
                height: 32px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                box-sizing: border-box !important;
            }
        }

        /* 性能优化：按钮样式类 */
        .btn-edit {
            background: rgba(24, 144, 255, 0.1);
            color: #1890ff;
            border: 1px solid rgba(24, 144, 255, 0.2);
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-edit:hover {
            background: rgba(24, 144, 255, 0.15);
            border-color: rgba(24, 144, 255, 0.3);
        }

        .btn-delete {
            background: rgba(255, 77, 79, 0.1);
            color: #ff4d4f;
            border: 1px solid rgba(255, 77, 79, 0.2);
            padding: 6px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-delete:hover {
            background: rgba(255, 77, 79, 0.15);
            border-color: rgba(255, 77, 79, 0.3);
        }

        /* 移动端递减复选框优化 */
        @media (max-width: 768px) {
            .decreasing-checkbox {
                font-size: 12px !important;
                padding: 3px 8px !important;
                border-radius: 4px !important;
                gap: 5px !important;
            }

            .decreasing-checkbox input[type="checkbox"] {
                width: 12px !important;
                height: 12px !important;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 登录页面 -->
        <div id="login-page" class="app-container">
            <div class="login-card">
                <div class="logo">📊</div>
                <h1 class="title">记账管理</h1>

                <form onsubmit="login(event)">
                    <div class="form-group">
                        <label>用户名/邮箱</label>
                        <input
                            type="text"
                            id="username"
                            placeholder="请输入用户名或邮箱"
                            required
                        >
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input
                            type="password"
                            id="password"
                            placeholder="请输入密码"
                            required
                        >
                    </div>
                    <button type="submit" class="btn" id="login-btn">
                        登录
                    </button>
                </form>

                <div id="message" style="margin-top: 15px; text-align: center;"></div>

                <div class="link">
                    <a href="#" onclick="toggleRegister()">
                        <span id="register-link-text">注册新账户</span>
                    </a>
                </div>

                <!-- 注册表单 -->
                <form id="register-form" onsubmit="register(event)" style="display: none; margin-top: 20px; border-top: 1px solid #eee; padding-top: 20px;">
                    <div class="form-group">
                        <label>用户名</label>
                        <input
                            type="text"
                            id="reg-username"
                            placeholder="请输入用户名"
                            required
                        >
                    </div>
                    <div class="form-group">
                        <label>邮箱</label>
                        <input
                            type="email"
                            id="reg-email"
                            placeholder="请输入邮箱"
                            required
                        >
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input
                            type="password"
                            id="reg-password"
                            placeholder="请输入密码（至少6位）"
                            required
                        >
                    </div>
                    <button type="submit" class="btn" id="register-btn">
                        注册
                    </button>
                </form>
            </div>
        </div>

        <!-- 主应用页面 -->
        <div id="main-page" class="app-container" style="display: none;">
            <div class="main-app">
                <div class="header">
                    <h2 id="main-title">我的账本</h2>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="showChangePassword()" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            修改密码
                        </button>
                        <button onclick="logout()" style="background: rgba(255,255,255,0.2); border: none; color: white; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                            退出
                        </button>
                    </div>
                </div>

                <div class="content">
                    <!-- 账本列表 -->
                    <div id="books-list">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                            <h3 style="margin: 0;">选择账本</h3>
                            <button onclick="showAddBookForm()" style="background: #1890ff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">+ 添加账本</button>
                        </div>
                        <div id="books-container">
                            <!-- 账本列表将在这里动态生成 -->
                        </div>
                    </div>

                    <!-- 记录列表 -->
                    <div id="records-list" style="display: none;">
                        <div class="records-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <!-- 左侧：账本切换器和管理按钮 -->
                            <div class="header-controls" style="display: flex; align-items: center; gap: 6px;">
                                <!-- 自定义下拉选择器 -->
                                <div class="custom-select" id="custom-book-selector" style="position: relative; background: #f0f0f0; border: 1px solid #ddd; padding: 6px 8px; border-radius: 4px; cursor: pointer; font-size: 13px; min-width: 110px; display: flex; align-items: center; justify-content: space-between; z-index: 1001;">
                                    <span id="selected-book-text">📚 默认账本</span>
                                    <span style="margin-left: 8px; font-size: 10px; color: #666;">▼</span>
                                    <!-- book-options不再嵌套在这里，由JS动态插入body下 -->
                                </div>
                                <button onclick="showBooksList()" style="background: #1890ff; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px; white-space: nowrap;">
                                    管理账本
                                </button>
                                <!-- 当前查看月份标识 -->
                                <div id="header-month-indicator" onclick="showMonthPicker()" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 6px 12px; border-radius: 4px; font-size: 12px; font-weight: 700; white-space: nowrap; box-shadow: 0 2px 8px rgba(245, 87, 108, 0.3); border: 1px solid rgba(255, 255, 255, 0.2); height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                                    📅 2025年5月
                                </div>
                            </div>
                            <!-- 右侧：记录统计 -->
                            <div class="header-stats">
                                <span id="records-count" style="color: #666; font-size: 13px;">0 条记录</span>
                            </div>
                        </div>

                        <!-- 统计表头 -->
                        <div id="statistics-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 8px; border-radius: 8px; margin-bottom: 12px; display: none; box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);">
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 6px; text-align: center;">
                                <div style="background: rgba(255, 255, 255, 0.1); padding: 6px; border-radius: 6px; backdrop-filter: blur(10px);">
                                    <div style="font-size: 10px; opacity: 0.9; font-weight: 500; margin-bottom: 1px;">💰 总金额</div>
                                    <div style="font-size: 13px; font-weight: 700; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);" id="stat-total-amount">¥0.00</div>
                                </div>
                                <div style="background: rgba(255, 255, 255, 0.1); padding: 6px; border-radius: 6px; backdrop-filter: blur(10px);">
                                    <div style="font-size: 10px; opacity: 0.9; font-weight: 500; margin-bottom: 1px;">📅 每月金额</div>
                                    <div style="font-size: 13px; font-weight: 700; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);" id="stat-monthly-amount">¥0.00</div>
                                </div>
                                <div style="background: rgba(255, 255, 255, 0.1); padding: 6px; border-radius: 6px; backdrop-filter: blur(10px);">
                                    <div style="font-size: 10px; opacity: 0.9; font-weight: 500; margin-bottom: 1px;">🔄 续期金额</div>
                                    <div style="font-size: 13px; font-weight: 700; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);" id="stat-renewal-amount">¥0.00</div>
                                </div>
                                <div style="background: rgba(255, 255, 255, 0.1); padding: 6px; border-radius: 6px; backdrop-filter: blur(10px);">
                                    <div style="font-size: 10px; opacity: 0.9; font-weight: 500; margin-bottom: 1px;">📈 累计金额</div>
                                    <div style="font-size: 13px; font-weight: 700; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);" id="stat-accumulated-amount">¥0.00</div>
                                </div>
                            </div>
                        </div>

                        <div id="records-container">
                            <!-- 记录列表将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- 月份导航栏 -->
            <div id="month-navigation" style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px); border-radius: 25px; padding: 8px 16px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); border: 1px solid rgba(255, 255, 255, 0.3); display: none; z-index: 1000;">
                <div style="display: flex; align-items: center; gap: 8px;">
                    <button id="prev-month-btn" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 20px; padding: 8px 16px; font-size: 12px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3); white-space: nowrap; min-width: 60px; display: flex; align-items: center; justify-content: center;">
                        ← <span id="prev-month-text">上月</span>
                    </button>
                    <div id="current-month-display" onclick="showMonthPicker()" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 8px 16px; border-radius: 20px; font-size: 13px; font-weight: 700; min-width: 100px; text-align: center; box-shadow: 0 2px 8px rgba(245, 87, 108, 0.3); white-space: nowrap; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                        2024年5月
                    </div>
                    <button id="next-month-btn" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 20px; padding: 8px 16px; font-size: 12px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3); white-space: nowrap; min-width: 60px; display: flex; align-items: center; justify-content: center;">
                        <span id="next-month-text">下月</span> →
                    </button>
                </div>
            </div>

            <button id="add-btn" onclick="showAddForm()" class="floating-btn" style="display: none;">+</button>
        </div>

        <!-- 下拉选项容器 -->
        <div id="book-options" style="display: none; position: absolute; background: white; border: 1px solid #ddd; border-radius: 4px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 9999; max-height: 200px; overflow-y: auto;">
            <!-- 账本选项将在这里动态生成 -->
        </div>

        <div id="renewal-options" style="display: none; position: absolute; background: white; border: 1px solid #ddd; border-radius: 4px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 10001; max-height: 200px; overflow-y: auto;">
            <!-- 续期选项将在这里动态生成 -->
        </div>

        <!-- 添加/编辑记录弹窗 -->
        <div id="add-form-modal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 10000;">
            <div style="background: white; padding: 20px; border-radius: 8px; width: 90%; max-width: 500px; max-height: 80vh; overflow-y: auto;">
                <h3 id="form-title" style="margin-bottom: 20px;">添加记录</h3>

                <!-- 累计收益编辑 -->
                <div id="accumulated-amount-display" style="display: none; margin-bottom: 15px;">
                    <div class="accumulated-amount-container" style="background: linear-gradient(135deg, #a8e6cf 0%, #88d8c0 50%, #7fb3d3 100%); border-radius: 16px; padding: 16px; position: relative; transition: all 0.3s ease; box-shadow: 0 4px 20px rgba(127, 179, 211, 0.3); border: none;">
                        <div class="accumulated-amount-display" style="display: flex; align-items: center; gap: 16px; width: 100%;">
                            <div style="display: flex; align-items: center; gap: 10px; flex-shrink: 0;">
                                <div class="amount-icon" style="background: rgba(255, 255, 255, 0.25); color: #2c3e50; width: 40px; height: 40px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 18px; font-weight: 600; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.3);">¥</div>
                                <div style="font-size: 16px; color: #2c3e50; font-weight: 700; white-space: nowrap;">累计收益</div>
                            </div>
                            <div style="flex: 1; min-width: 0; display: flex; justify-content: center; padding: 0 8px; overflow: visible;">
                                <input type="number" step="0.01" id="record-accumulated-amount" placeholder="0.00" class="amount-input auto-resize-input" style="background: transparent; border: none; color: #2c3e50; font-size: 24px; font-weight: 700; min-width: 120px; width: 150px; max-width: none; outline: none; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif; text-align: center; overflow: visible;" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 创建时间显示 -->
                <div id="created-time-display" style="display: none; margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #1890ff;">
                    <div style="font-size: 12px; color: #666; margin-bottom: 4px;">📅 创建时间</div>
                    <div id="record-created-time" style="font-size: 14px; color: #333; font-weight: 600;"></div>
                </div>

                <form onsubmit="submitRecord(event)">
                    <div class="form-group">
                        <label>日期</label>
                        <input type="date" id="record-date" required>
                    </div>
                    <div class="form-group">
                        <label>姓名</label>
                        <input type="text" id="record-name" placeholder="请输入姓名" required>
                    </div>
                    <div class="form-group">
                        <label>金额</label>
                        <input type="number" step="0.01" id="record-amount" placeholder="0.00" required>
                    </div>
                    <div class="form-group">
                        <label style="display: flex; align-items: baseline; gap: 12px; margin-bottom: 8px;">
                            <span style="font-size: 14px; line-height: 1;">每月金额</span>
                            <label class="decreasing-checkbox" style="display: inline-flex; align-items: center; gap: 4px; cursor: pointer; background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; font-weight: 700; box-shadow: 0 1px 2px rgba(255, 107, 53, 0.3); transition: all 0.2s ease; white-space: nowrap; line-height: 1; vertical-align: baseline;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                                <input type="checkbox" id="record-is-decreasing" onchange="toggleDecreasingMode()" style="margin: 0; width: 10px; height: 10px;">
                                每月递减
                            </label>
                        </label>
                        <input type="number" step="0.01" id="record-monthly-amount" placeholder="0.00" required>
                        <div id="decreasing-info" style="display: none; margin-top: 8px; padding: 8px; background: #f0f8ff; border: 1px solid #b3d9ff; border-radius: 4px; font-size: 12px; color: #0066cc;">
                            💡 递减形式：每月完成后会自动递减每月金额，直至金额为0时结束此记账
                        </div>
                    </div>
                    <div class="form-group">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; flex-wrap: wrap; gap: 8px;">
                            <label style="margin: 0;">续期时间</label>
                            <div style="display: flex; align-items: center; gap: 6px; flex-shrink: 0;">
                                <div id="next-renewal-date" style="font-size: 14px; color: #1890ff; font-weight: 600;">下次续期: --</div>
                                <div style="font-size: 12px; color: #666; white-space: nowrap;">(根据前面时间变动)</div>
                            </div>
                        </div>
                        <div id="custom-renewal-selector" style="position: relative; width: 100%; padding: 8px 12px; border: 1px solid #ddd; border-radius: 4px; background: white; cursor: pointer; display: flex; justify-content: space-between; align-items: center; transition: border-color 0.2s;">
                            <span id="selected-renewal-text" style="color: #999;">请选择</span>
                            <span style="color: #999; font-size: 12px;">▼</span>
                        </div>
                        <input type="hidden" id="record-renewal-time" required>
                    </div>
                    <div class="form-group">
                        <label>续期金额 <small style="color: #666;">(可选，不填默认为0)</small></label>
                        <input type="number" step="0.01" id="record-renewal-amount" placeholder="0.00">
                    </div>
                    <div class="form-group">
                        <label>备注</label>
                        <textarea id="record-remark" placeholder="请输入备注（可选）" rows="3" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button type="submit" class="btn" style="flex: 1;">保存</button>
                        <button type="button" onclick="hideAddForm()" style="flex: 1; background: #f0f0f0; color: #333;">取消</button>
                    </div>
                </form>
            </div>
        </div>



        <!-- 添加账本弹窗 -->
        <div id="add-book-modal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 10000;">
            <div style="background: white; padding: 20px; border-radius: 8px; width: 90%; max-width: 400px;">
                <h3 style="margin-bottom: 20px;">📚 添加账本</h3>
                <form onsubmit="submitBook(event)">
                    <div class="form-group">
                        <label>账本名称</label>
                        <input type="text" id="book-name" placeholder="请输入账本名称" required>
                    </div>
                    <div class="form-group">
                        <label>描述</label>
                        <textarea id="book-description" placeholder="请输入账本描述（可选）" rows="3" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;"></textarea>
                    </div>
                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                        <button type="submit" class="btn" style="flex: 1;">创建账本</button>
                        <button type="button" onclick="hideAddBookForm()" style="flex: 1; background: #f0f0f0; color: #333; border: none; padding: 10px; border-radius: 4px; cursor: pointer;">取消</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 修改密码弹窗 -->
        <div id="change-password-modal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 10000;">
            <div style="background: white; padding: 20px; border-radius: 8px; width: 90%; max-width: 400px;">
                <h3 style="margin-bottom: 20px;">🔒 修改密码</h3>
                <form onsubmit="changePassword(event)">
                    <div class="form-group">
                        <label>当前密码</label>
                        <input type="password" id="current-password" placeholder="请输入当前密码" required>
                    </div>
                    <div class="form-group">
                        <label>新密码</label>
                        <input type="password" id="new-password" placeholder="请输入新密码（至少6位）" required minlength="6">
                    </div>
                    <div class="form-group">
                        <label>确认新密码</label>
                        <input type="password" id="confirm-password" placeholder="请再次输入新密码" required>
                    </div>
                    <div style="display: flex; gap: 10px; margin-top: 20px;">
                        <button type="submit" class="btn" style="flex: 1;">确认修改</button>
                        <button type="button" onclick="hideChangePassword()" style="flex: 1; background: #f0f0f0; color: #333; border: none; padding: 10px; border-radius: 4px; cursor: pointer;">取消</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 月份选择器弹窗 -->
        <div id="month-picker-modal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); align-items: center; justify-content: center; z-index: 10000;">
            <div style="background: white; padding: 20px; border-radius: 12px; width: 90%; max-width: 400px; box-shadow: 0 8px 32px rgba(0,0,0,0.2);">
                <h3 style="margin: 0 0 20px 0; text-align: center; color: #333; font-size: 18px;">📅 选择年月</h3>

                <!-- 年份选择 -->
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #555;">年份</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <button type="button" onclick="changePickerYear(-1)" style="background: #f0f0f0; border: none; border-radius: 6px; padding: 8px 12px; cursor: pointer; font-size: 16px; font-weight: 600; color: #666; transition: all 0.2s;">←</button>
                        <div id="picker-year" style="flex: 1; text-align: center; font-size: 20px; font-weight: 700; color: #333; padding: 8px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 8px;">2025</div>
                        <button type="button" onclick="changePickerYear(1)" style="background: #f0f0f0; border: none; border-radius: 6px; padding: 8px 12px; cursor: pointer; font-size: 16px; font-weight: 600; color: #666; transition: all 0.2s;">→</button>
                    </div>
                </div>

                <!-- 月份选择 -->
                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600; color: #555;">月份</label>
                    <div id="month-grid" style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 8px;">
                        <!-- 月份按钮将由JavaScript生成 -->
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div style="display: flex; gap: 10px; margin-top: 20px;">
                    <button type="button" onclick="confirmMonthPicker()" style="flex: 1; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; border: none; padding: 12px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 14px; transition: all 0.2s;">确认</button>
                    <button type="button" onclick="hideMonthPicker()" style="flex: 1; background: #f0f0f0; color: #333; border: none; padding: 12px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 14px; transition: all 0.2s;">取消</button>
                </div>
            </div>
        </div>

        <!-- 递减记账编辑确认弹窗 -->
        <div id="decreasing-edit-confirm-modal" style="display: none; position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.6); align-items: center; justify-content: center; z-index: 10001;">
            <div style="background: white; padding: 24px; border-radius: 12px; width: 90%; max-width: 450px; box-shadow: 0 8px 32px rgba(0,0,0,0.2);">
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 48px; margin-bottom: 12px;">⚠️</div>
                    <h3 style="margin: 0 0 8px 0; color: #ff6b35; font-size: 20px;">编辑递减记账提醒</h3>
                    <p style="margin: 0; color: #666; font-size: 14px; line-height: 1.5;">您正在编辑一个递减形式的记账记录</p>
                </div>

                <div style="background: #fff3e0; border: 1px solid #ffcc80; border-radius: 8px; padding: 16px; margin-bottom: 20px;">
                    <div style="font-weight: 600; color: #e65100; margin-bottom: 8px; font-size: 14px;">⚠️ 重要提醒：</div>
                    <ul style="margin: 0; padding-left: 16px; color: #bf360c; font-size: 13px; line-height: 1.6;">
                        <li>修改递减记账可能会影响已有的累计金额计算</li>
                        <li>修改金额会重新计算所有月份的剩余金额</li>
                        <li>修改后可能需要重新调整完成状态</li>
                        <li>建议谨慎修改，确保数据准确性</li>
                    </ul>
                </div>

                <div style="background: #f8f9fa; border-radius: 8px; padding: 12px; margin-bottom: 20px;">
                    <div style="font-size: 13px; color: #666; text-align: center;">
                        <strong>当前记录：</strong><span id="confirm-record-name" style="color: #333;"></span><br>
                        <strong>原始金额：</strong><span id="confirm-record-amount" style="color: #333;"></span><br>
                        <strong>每月递减：</strong><span id="confirm-monthly-amount" style="color: #333;"></span>
                    </div>
                </div>

                <div style="display: flex; gap: 12px;">
                    <button type="button" onclick="cancelDecreasingEdit()" style="flex: 1; background: #f0f0f0; color: #333; border: none; padding: 12px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 14px; transition: all 0.2s;">取消编辑</button>
                    <button type="button" onclick="confirmDecreasingEdit()" style="flex: 1; background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); color: white; border: none; padding: 12px; border-radius: 8px; cursor: pointer; font-weight: 600; font-size: 14px; transition: all 0.2s;">确认编辑</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let token = '';
        let currentBook = null;
        let books = [];
        let records = [];
        let currentViewMonth = new Date().toISOString().slice(0, 7); // 当前查看月份，格式: 2024-05

        // 月份选择器变量
        let pickerYear = new Date().getFullYear();
        let pickerMonth = new Date().getMonth() + 1;
        let selectedMonth = null;

        // 递减记账编辑确认变量
        let pendingDecreasingEdit = null;

        // DOM缓存 - 减少重复查询，提升性能
        const DOMCache = {
            elements: {},

            get(id) {
                if (!this.elements[id]) {
                    this.elements[id] = document.getElementById(id);
                }
                return this.elements[id];
            },

            clear() {
                this.elements = {};
            }
        };

        // 防抖函数 - 减少频繁操作，提升性能
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 请求去重 - 防止重复请求，不缓存结果
        class RequestDeduplicator {
            constructor() {
                this.pendingRequests = new Map();
            }

            async request(url, options) {
                const key = `${url}_${JSON.stringify(options)}`;

                if (this.pendingRequests.has(key)) {
                    return this.pendingRequests.get(key);
                }

                const promise = fetch(url, options)
                    .finally(() => {
                        this.pendingRequests.delete(key);
                    });

                this.pendingRequests.set(key, promise);
                return promise;
            }
        }

        const deduplicator = new RequestDeduplicator();





        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            // 设置设备类型类名
            if (window.innerWidth > 768) {
                document.body.classList.add('desktop-mode');
            } else {
                document.body.classList.add('mobile-mode');
            }

            // 监听窗口大小变化
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    document.body.classList.add('desktop-mode');
                    document.body.classList.remove('mobile-mode');
                } else {
                    document.body.classList.add('mobile-mode');
                    document.body.classList.remove('desktop-mode');
                }
            });

            checkAuth();
            // 设置默认日期
            document.getElementById('record-date').value = new Date().toISOString().split('T')[0];

            // 初始化自定义下拉选择器事件
            initCustomSelector();

            // 清理任何可能存在的旧select元素
            cleanupOldSelectors();

            // 下拉选项容器已在HTML中定义，无需重复创建

            // 初始化续期时间选择器
            initRenewalSelector();

            // 初始化简单的滚动回弹效果
            initSimpleBounce();

            // 初始化自动调整输入框宽度
            initAutoResizeInput();

            // 初始化下次续期时间计算
            initNextRenewalCalculation();

            // 初始化月份导航
            initMonthNavigation();
        });

        function cleanupOldSelectors() {
            // 移除任何可能存在的旧的select元素
            const oldSelectors = document.querySelectorAll('#book-selector, select[id*="book"]');
            oldSelectors.forEach(selector => {
                if (selector && selector.id !== 'custom-book-selector') {
                    selector.remove();
                }
            });
        }

        function initCustomSelector() {
            document.addEventListener('click', function(e) {
                const customSelector = document.getElementById('custom-book-selector');
                const bookOptions = document.getElementById('book-options');
                if (!customSelector || !bookOptions) return;
                if (customSelector.contains(e.target)) {
                    if (bookOptions.style.display === 'none' || bookOptions.style.display === '') {
                        showBookOptionsDropdown();
                        setTimeout(() => {
                            const currentOption = document.getElementById('current-book-option');
                            if (currentOption) {
                                currentOption.scrollIntoView({ block: 'nearest' });
                            }
                        }, 0);
                    } else {
                        hideBookOptionsDropdown();
                    }
                } else {
                    hideBookOptionsDropdown();
                }
            });
        }

        // 优化的自动调整输入框宽度
        function initAutoResizeInput() {
            let measurer = null;
            let resizeTimeout = null;

            function createMeasurer() {
                if (!measurer) {
                    measurer = document.createElement('span');
                    measurer.style.cssText = `
                        position: absolute;
                        visibility: hidden;
                        white-space: nowrap;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
                        font-size: 24px;
                        font-weight: 700;
                        padding: 0;
                        margin: 0;
                        border: none;
                    `;
                    document.body.appendChild(measurer);
                }
                return measurer;
            }

            function adjustInputWidth(input) {
                if (!input) return;

                const text = input.value || input.placeholder || '0.00';
                const textMeasurer = createMeasurer();
                textMeasurer.textContent = text;
                const textWidth = textMeasurer.offsetWidth;

                // 简化的宽度计算
                const isMobile = window.innerWidth <= 768;
                const minWidth = isMobile ? 80 : 120;
                const extraSpace = isMobile ? 25 : 40;

                const newWidth = Math.max(minWidth, textWidth + extraSpace);
                input.style.width = newWidth + 'px';
            }

            // 防抖的调整函数
            function debouncedAdjust(input) {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => adjustInputWidth(input), 100);
            }

            // 监听累计金额输入框
            const amountInput = document.getElementById('record-accumulated-amount');
            if (amountInput) {
                // 初始调整
                adjustInputWidth(amountInput);

                // 优化的事件监听
                amountInput.addEventListener('input', () => debouncedAdjust(amountInput));
                amountInput.addEventListener('focus', function() {
                    const currentWidth = parseInt(this.style.width) || 120;
                    this.style.width = Math.max(currentWidth, 150) + 'px';
                });
                amountInput.addEventListener('blur', () => adjustInputWidth(amountInput));

                // 优化的窗口大小变化监听
                let resizeTimer = null;
                window.addEventListener('resize', () => {
                    clearTimeout(resizeTimer);
                    resizeTimer = setTimeout(() => adjustInputWidth(amountInput), 200);
                });
            }
        }

        // 初始化下次续期时间计算
        function initNextRenewalCalculation() {
            const dateInput = document.getElementById('record-date');

            // 监听日期变化
            if (dateInput) {
                dateInput.addEventListener('change', updateNextRenewalDate);
                dateInput.addEventListener('input', updateNextRenewalDate);
            }

            // 初始计算
            updateNextRenewalDate();
        }

        // 优化的滚动回弹效果
        function initSimpleBounce() {
            const content = document.querySelector('.content');
            if (!content) return;

            let bounceTimeout;
            let isAtTop = false;
            let isAtBottom = false;
            let ticking = false;

            function handleScroll() {
                if (!ticking) {
                    requestAnimationFrame(() => {
                        const scrollTop = content.scrollTop;
                        const scrollHeight = content.scrollHeight;
                        const clientHeight = content.clientHeight;

                        // 清除之前的回弹动画
                        clearTimeout(bounceTimeout);
                        content.classList.remove('bounce-top', 'bounce-bottom');

                        // 检测是否滚动到顶部
                        if (scrollTop <= 5 && !isAtTop) {
                            isAtTop = true;
                            isAtBottom = false;
                            content.classList.add('bounce-top');
                            setTimeout(() => {
                                content.classList.remove('bounce-top');
                            }, 300);
                        }
                        // 检测是否滚动到底部
                        else if (scrollTop + clientHeight >= scrollHeight - 5 && !isAtBottom) {
                            isAtBottom = true;
                            isAtTop = false;
                            content.classList.add('bounce-bottom');
                            setTimeout(() => {
                                content.classList.remove('bounce-bottom');
                            }, 300);
                        }
                        // 重置状态
                        else if (scrollTop > 10 && scrollTop + clientHeight < scrollHeight - 10) {
                            isAtTop = false;
                            isAtBottom = false;
                        }

                        ticking = false;
                    });
                    ticking = true;
                }
            }

            content.addEventListener('scroll', handleScroll, { passive: true });
        }

        function checkAuth() {
            const savedToken = localStorage.getItem('token');
            if (savedToken) {
                token = savedToken;
                showMainPage();
                loadBooks();
            }
        }

        function showMessage(text, isError = false) {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.style.color = isError ? '#ff4d4f' : '#52c41a';
            messageEl.style.display = 'block';
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 3000);
        }

        function toggleRegister() {
            const loginForm = document.querySelector('#login-page form:first-of-type');
            const registerForm = document.getElementById('register-form');
            const linkText = document.getElementById('register-link-text');

            if (registerForm.style.display === 'none' || registerForm.style.display === '') {
                // 显示注册表单，隐藏登录表单
                loginForm.style.display = 'none';
                registerForm.style.display = 'block';
                linkText.textContent = '返回登录';
            } else {
                // 显示登录表单，隐藏注册表单
                loginForm.style.display = 'block';
                registerForm.style.display = 'none';
                linkText.textContent = '注册新账户';
            }
        }


        async function login(event) {
            event.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('login-btn');

            loginBtn.textContent = '登录中...';
            loginBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('action', 'login');
                formData.append('username', username);
                formData.append('password', password);

                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    token = data.data.token;
                    localStorage.setItem('token', token);
                    showMessage('登录成功！');
                    // 立即显示主页面并加载账本，不需要延迟
                    showMainPage();
                    loadBooks();
                } else {
                    showMessage(data.error || '登录失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
            }

            loginBtn.textContent = '登录';
            loginBtn.disabled = false;
        }

        async function register(event) {
            event.preventDefault();
            const username = document.getElementById('reg-username').value;
            const email = document.getElementById('reg-email').value;
            const password = document.getElementById('reg-password').value;
            const registerBtn = document.getElementById('register-btn');

            registerBtn.textContent = '注册中...';
            registerBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('action', 'register');
                formData.append('username', username);
                formData.append('email', email);
                formData.append('password', password);

                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    token = data.data.token;
                    localStorage.setItem('token', token);
                    showMessage('注册成功！');
                    // 立即显示主页面并加载账本，不需要延迟
                    showMainPage();
                    loadBooks();
                } else {
                    showMessage(data.error || '注册失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
            }

            registerBtn.textContent = '注册';
            registerBtn.disabled = false;
        }
        function showMainPage() {
            document.getElementById('login-page').style.display = 'none';
            document.getElementById('main-page').style.display = 'block';
        }

        function showBooksList() {
            document.getElementById('books-list').style.display = 'block';
            document.getElementById('records-list').style.display = 'none';
            document.getElementById('add-btn').style.display = 'none';
            document.getElementById('main-title').textContent = '我的账本';
            currentBook = null;

            // 隐藏月份导航栏
            document.getElementById('month-navigation').style.display = 'none';
        }

        function showRecordsList(book) {
            document.getElementById('books-list').style.display = 'none';
            document.getElementById('records-list').style.display = 'block';
            document.getElementById('add-btn').style.display = 'block';
            document.getElementById('main-title').textContent = book.name;
            currentBook = book;

            // 显示月份导航栏
            document.getElementById('month-navigation').style.display = 'block';
            updateMonthDisplay();

            // 保存最后选择的账本
            localStorage.setItem('lastSelectedBookId', book.id);

            // 清理旧的select元素
            cleanupOldSelectors();

            // 更新账本选择器
            updateBookSelector();

            loadRecords();
        }

        function updateBookSelector() {
            const selectedText = document.getElementById('selected-book-text');
            const optionsContainer = document.getElementById('book-options');

            if (!selectedText || !optionsContainer) return;

            // 移除任何可能存在的旧的select元素
            const oldSelector = document.getElementById('book-selector');
            if (oldSelector) {
                oldSelector.remove();
            }

            // 更新选中的文本
            if (currentBook) {
                selectedText.textContent = `📚 ${currentBook.name}`;
            }

            // 清空并重新生成选项
            optionsContainer.innerHTML = '';

            books.forEach(book => {
                const option = document.createElement('div');
                option.style.cssText = 'padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #f0f0f0; transition: background-color 0.2s;';
                option.textContent = `📚 ${book.name}`;
                option.onclick = () => selectBook(book);

                // 高亮当前选中的账本，并添加唯一id
                if (currentBook && book.id == currentBook.id) {
                    option.style.backgroundColor = '#e6f7ff';
                    option.style.color = '#1890ff';
                    option.style.fontWeight = '600';
                    option.id = 'current-book-option';
                }

                // 悬停效果
                option.onmouseenter = () => {
                    if (!currentBook || book.id != currentBook.id) {
                        option.style.backgroundColor = '#f5f5f5';
                    }
                };
                option.onmouseleave = () => {
                    if (!currentBook || book.id != currentBook.id) {
                        option.style.backgroundColor = '';
                    }
                };

                optionsContainer.appendChild(option);
            });
        }

        function selectBook(book) {
            if (book && book.id != currentBook.id) {
                // 隐藏下拉选项
                document.getElementById('book-options').style.display = 'none';
                // 切换到选中的账本
                showRecordsList(book);
            } else {
                // 如果选择的是当前账本，只是隐藏下拉选项
                document.getElementById('book-options').style.display = 'none';
            }
        }

        // switchBook函数已移除，因为我们使用自定义下拉选择器

        function autoEnterDefaultBook() {
            if (books.length === 0) return;

            // 获取最后选择的账本ID
            const lastSelectedBookId = localStorage.getItem('lastSelectedBookId');
            let targetBook = null;

            // 优先选择最后选择的账本
            if (lastSelectedBookId) {
                targetBook = books.find(book => book.id == lastSelectedBookId);
            }

            // 如果没有找到最后选择的账本，选择第一个账本作为默认账本
            if (!targetBook && books.length > 0) {
                targetBook = books[0];
            }

            // 直接进入目标账本，无延迟
            if (targetBook) {
                showRecordsList(targetBook);
            }
        }

        async function loadBooks() {
            if (!token) {
                document.getElementById('main-page').style.display = 'none';
                document.getElementById('login-page').style.display = 'block';
                showMessage('请先登录', true);
                return;
            }

            try {
                const response = await fetch('api_direct.php?action=get_books', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    books = data.data;
                    renderBooks();

                    // 自动进入默认账本或最后选择的账本
                    autoEnterDefaultBook();
                } else {
                    // 如果是认证错误，清除token并返回登录页
                    if (response.status === 401 || (data.error && data.error.includes('token'))) {
                        localStorage.removeItem('token');
                        localStorage.removeItem('lastSelectedBookId');
                        token = '';
                        document.getElementById('main-page').style.display = 'none';
                        document.getElementById('login-page').style.display = 'block';
                        showMessage('登录已过期，请重新登录', true);
                    } else {
                        showMessage(data.error || '加载账本失败', true);
                    }
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
            }
        }

        // 格式化账本创建时间
        function formatBookCreateTime(dateStr) {
            if (!dateStr) return '未知';
            const date = new Date(dateStr);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}`;
        }

        // 格式化记录创建时间
        function formatRecordCreateTime(dateStr) {
            if (!dateStr) return '未知';
            const date = new Date(dateStr);
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            return `${month}-${day} ${hours}:${minutes}`;
        }

        function renderBooks() {
            const container = document.getElementById('books-container');
            container.innerHTML = '';

            books.forEach(book => {
                const bookEl = document.createElement('div');
                bookEl.style.cssText = 'padding: 20px; border: 1px solid #e8e8e8; border-radius: 16px; margin-bottom: 16px; cursor: pointer; background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%); box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); transition: all 0.3s ease;';
                bookEl.onmouseenter = () => {
                    bookEl.style.transform = 'translateY(-4px)';
                    bookEl.style.boxShadow = '0 8px 24px rgba(0, 0, 0, 0.15)';
                    bookEl.style.borderColor = '#d9d9d9';
                };
                bookEl.onmouseleave = () => {
                    bookEl.style.transform = 'translateY(0)';
                    bookEl.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.08)';
                    bookEl.style.borderColor = '#e8e8e8';
                };
                bookEl.onclick = () => showRecordsList(book);

                bookEl.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <div style="font-size: 20px; font-weight: 700; color: #1a1a1a;">📚 ${book.name}</div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: 600;">
                                ${book.record_count || 0} 条记录
                            </div>
                            <button onclick="deleteBook(${book.id}, '${book.name}'); event.stopPropagation();"
                                    style="background: rgba(255, 77, 79, 0.1); color: #ff4d4f; border: 1px solid rgba(255, 77, 79, 0.2); padding: 4px 8px; border-radius: 6px; cursor: pointer; font-size: 12px; font-weight: 500; transition: all 0.2s ease;"
                                    onmouseover="this.style.background='rgba(255, 77, 79, 0.15)'; this.style.borderColor='rgba(255, 77, 79, 0.3)'"
                                    onmouseout="this.style.background='rgba(255, 77, 79, 0.1)'; this.style.borderColor='rgba(255, 77, 79, 0.2)'"
                                    title="删除账本">🗑️</button>
                        </div>
                    </div>
                    <div style="color: #666; font-size: 14px; margin-bottom: 12px; line-height: 1.4;">${book.description || '✨ 暂无描述'}</div>
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <div style="background: rgba(82, 196, 26, 0.1); color: #52c41a; padding: 6px 12px; border-radius: 8px; font-weight: 600; font-size: 14px;">
                            💰 累计: ¥${parseFloat(book.total_amount || 0).toFixed(2)}
                        </div>
                        <div style="color: #1890ff; font-size: 12px; font-weight: 500;">点击进入 →</div>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; font-size: 11px; color: #999;">
                        <div style="font-weight: 600; color: #666;">📅 创建时间: ${formatBookCreateTime(book.created_at)}</div>
                    </div>
                `;

                container.appendChild(bookEl);
            });

            // 移除任何可能存在的旧的select元素
            const oldSelector = document.getElementById('book-selector');
            if (oldSelector) {
                oldSelector.remove();
            }
        }
        async function loadRecords() {
            if (!currentBook) return;

            if (!token) {
                showMessage('请先登录', true);
                return;
            }

            try {
                const response = await fetch(`api_direct.php?action=get_records&book_id=${currentBook.id}&view_month=${currentViewMonth}`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                const data = await response.json();

                if (data.success) {
                    records = data.data;
                    clearRenewalCache(); // 清理缓存
                    renderRecords();
                } else {
                    // 如果是认证错误，清除token并返回登录页
                    if (response.status === 401 || (data.error && data.error.includes('token'))) {
                        localStorage.removeItem('token');
                        localStorage.removeItem('lastSelectedBookId');
                        token = '';
                        document.getElementById('main-page').style.display = 'none';
                        document.getElementById('login-page').style.display = 'block';
                        showMessage('登录已过期，请重新登录', true);
                    } else {
                        showMessage(data.error || '加载记录失败', true);
                    }
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
                console.error('加载记录错误:', error);
            }
        }

        // 优化的续期月份判断函数
        const renewalCache = new Map();

        // 清理缓存的函数
        function clearRenewalCache() {
            if (renewalCache.size > 1000) { // 防止缓存过大
                renewalCache.clear();
            }
        }

        function isRenewalMonth(record, viewMonth = currentViewMonth) {
            if (!record.date || !record.renewal_time || record.renewal_time === '永久') {
                return false;
            }

            // 使用缓存提高性能
            const cacheKey = `${record.id}-${record.date}-${record.renewal_time}-${viewMonth}`;
            if (renewalCache.has(cacheKey)) {
                return renewalCache.get(cacheKey);
            }

            const monthsToAdd = {
                '二个月': 2,
                '三个月': 3,
                '六个月': 6
            };

            const addMonths = monthsToAdd[record.renewal_time];
            if (!addMonths) {
                renewalCache.set(cacheKey, false);
                return false;
            }

            // 优化的日期计算 - 使用数学计算而不是循环
            const recordDate = new Date(record.date);
            const viewDate = new Date(viewMonth + '-01');

            const recordYear = recordDate.getFullYear();
            const recordMonthNum = recordDate.getMonth();
            const viewYear = viewDate.getFullYear();
            const viewMonthNum = viewDate.getMonth();

            // 计算月份差
            const monthDiff = (viewYear - recordYear) * 12 + (viewMonthNum - recordMonthNum);

            // 检查是否为续期月份（包括创建月份和后续续期月份）
            const isRenewal = monthDiff >= 0 && monthDiff % addMonths === 0;

            renewalCache.set(cacheKey, isRenewal);
            return isRenewal;
        }

        function renderRecords() {
            const container = document.getElementById('records-container');
            const countEl = document.getElementById('records-count');

            // 使用DocumentFragment提高性能
            const fragment = document.createDocumentFragment();
            countEl.textContent = `${records.length} 条记录`;

            // 计算统计数据
            updateStatistics();

            // 显示统计表头
            document.getElementById('statistics-header').style.display = records.length > 0 ? 'block' : 'none';

            // 按日期的"日"升序排序
            const sortedRecords = [...records].sort((a, b) => {
                const dateA = new Date(a.date || '1900-01-01');
                const dateB = new Date(b.date || '1900-01-01');

                // 只按日排序
                return dateA.getDate() - dateB.getDate();
            });

            sortedRecords.forEach(record => {
                const recordEl = document.createElement('div');
                let cardClasses = ['record-card'];

                // 判断是否已完成
                if (record.current_completed) {
                    cardClasses.push('completed');
                }

                // 判断是否为续期月份
                if (isRenewalMonth(record)) {
                    cardClasses.push('renewal-month');
                }

                recordEl.className = cardClasses.join(' ');

                // 判断是否为当月操作（基于最后更新时间）
                const lastUpdateTime = record.updated_at || record.created_at;
                const lastUpdateMonth = lastUpdateTime ? lastUpdateTime.substring(0, 7) : '';
                const currentMonth = new Date().toISOString().substring(0, 7);
                const isCurrentMonth = lastUpdateMonth === currentMonth;
                const lockStatus = isCurrentMonth ? '' : ' 🔒';

                // 格式化日期显示（只显示日）
                const formatDate = (dateStr) => {
                    if (!dateStr) return '';
                    const date = new Date(dateStr);
                    return `${String(date.getDate()).padStart(2, '0')}日`;
                };



                // 格式化续期信息 - 计算下次续期月份或递减信息
                const formatRenewalInfo = (record) => {
                    // 递减形式显示递减信息
                    if (record.is_decreasing == 1) {
                        return `每月递减:¥${parseFloat(record.monthly_amount || 0).toFixed(2)}`;
                    }

                    // 普通形式显示续期信息
                    const dateStr = record.date;
                    const renewalTime = record.renewal_time;
                    const renewalAmount = record.renewal_amount;

                    if (!renewalTime || renewalTime === '永久' || !dateStr) {
                        return `续:¥${parseFloat(renewalAmount || 0).toFixed(2)}`;
                    }

                    const recordDate = new Date(dateStr);
                    const monthsToAdd = {
                        '二个月': 2,
                        '三个月': 3,
                        '六个月': 6
                    };

                    const addMonths = monthsToAdd[renewalTime];
                    if (!addMonths) {
                        return `续:¥${parseFloat(renewalAmount || 0).toFixed(2)}`;
                    }

                    // 计算下次续期月份
                    const nextRenewalDate = new Date(recordDate);
                    nextRenewalDate.setMonth(nextRenewalDate.getMonth() + addMonths);
                    const nextMonth = nextRenewalDate.getMonth() + 1;

                    return `${nextMonth}月续:¥${parseFloat(renewalAmount || 0).toFixed(2)}`;
                };

                // 检查是否为已结束的递减记录
                const isFinished = record.is_decreasing == 1 && record.is_finished == 1;
                const checkboxDisabled = isFinished ? 'disabled' : '';
                const finishedStyle = isFinished ? 'opacity: 0.6; background: #f5f5f5;' : '';
                const finishedMessage = isFinished ? '<div style="color: #ff4d4f; font-size: 12px; margin-top: 4px;">⚠️ 此记账已清零，请编辑或删除此记账</div>' : '';

                // 计算显示金额（根据查看月份）
                let displayAmount;
                if (record.is_decreasing == 1) {
                    // 递减记账：所有月份都使用实时计算
                    displayAmount = calculateHistoricalRemainingAmount(record, currentViewMonth);
                } else {
                    // 普通记账：始终使用原始金额
                    displayAmount = parseFloat(record.amount || 0);
                }

                recordEl.innerHTML = `
                    <div class="record-header" style="${finishedStyle}">
                        <div class="left-section">
                            <input type="checkbox" ${record.current_completed ? 'checked' : ''} ${checkboxDisabled}
                                   onchange="toggleRecord(${record.id})" onclick="event.stopPropagation()">
                            <span class="record-name">${record.name || ''}${isRenewalMonth(record) ? '<span class="status-badge renewal">续期</span>' : ''}${isFinished ? '<span class="status-badge" style="background: #ff4d4f;">已结束</span>' : ''}</span>
                            <div class="record-amount">¥${displayAmount.toFixed(2)}</div>
                            <div class="accumulated">累计:¥${parseFloat(record.accumulated_amount || 0).toFixed(2)}</div>
                            <div class="record-details-inline">
                                <span>${formatDate(record.date)}</span>
                                <span>月:¥${parseFloat(record.monthly_amount || 0).toFixed(2)}</span>
                                <span>${formatRenewalInfo(record)}</span>
                                ${record.remark ? `<span>备注:${record.remark}</span>` : ''}
                                ${lockStatus ? `<span style="color: #999;">${lockStatus}</span>` : ''}
                            </div>
                        </div>
                        <div class="right-section">
                            <button onclick="editRecord(${record.id}); event.stopPropagation();" style="background: rgba(24, 144, 255, 0.1); color: #1890ff; border: 1px solid rgba(24, 144, 255, 0.2); padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 12px; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.background='rgba(24, 144, 255, 0.15)'; this.style.borderColor='rgba(24, 144, 255, 0.3)'" onmouseout="this.style.background='rgba(24, 144, 255, 0.1)'; this.style.borderColor='rgba(24, 144, 255, 0.2)'">✏️</button>
                            <button onclick="deleteRecord(${record.id}); event.stopPropagation();" style="background: rgba(255, 77, 79, 0.1); color: #ff4d4f; border: 1px solid rgba(255, 77, 79, 0.2); padding: 6px 12px; border-radius: 6px; cursor: pointer; font-size: 12px; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.background='rgba(255, 77, 79, 0.15)'; this.style.borderColor='rgba(255, 77, 79, 0.3)'" onmouseout="this.style.background='rgba(255, 77, 79, 0.1)'; this.style.borderColor='rgba(255, 77, 79, 0.2)'">🗑️</button>
                        </div>
                    </div>
                    <div class="record-details">
                        <div class="date-highlight">${formatDate(record.date)}</div>
                        <div>月:¥${parseFloat(record.monthly_amount || 0).toFixed(2)}</div>
                        <div>${formatRenewalInfo(record)}</div>
                        ${record.remark ? `<div>备注:${record.remark}</div>` : ''}
                        ${finishedMessage}
                    </div>
                `;

                // 为记录卡片添加点击事件来切换完成状态
                recordEl.addEventListener('click', function(event) {
                    // 如果点击的是复选框、按钮或其他交互元素，不触发卡片点击
                    if (event.target.tagName === 'INPUT' ||
                        event.target.tagName === 'BUTTON' ||
                        event.target.closest('button') ||
                        event.target.closest('input')) {
                        return;
                    }

                    // 如果记录已结束，不允许点击切换
                    if (isFinished) {
                        return;
                    }

                    // 触发复选框切换
                    toggleRecord(record.id);
                });

                fragment.appendChild(recordEl);
            });

            // 一次性添加所有元素到DOM
            container.innerHTML = '';
            container.appendChild(fragment);
        }

        function updateStatistics() {
            // 使用reduce优化计算性能
            const totals = records.reduce((acc, record) => {
                // 递减记账需要根据查看月份计算历史状态
                if (record.is_decreasing == 1) {
                    // 递减记账：所有月份都使用实时计算
                    const historicalRemainingAmount = calculateHistoricalRemainingAmount(record, currentViewMonth);
                    acc.totalAmount += historicalRemainingAmount;
                } else {
                    // 普通记账：始终使用原始金额
                    acc.totalAmount += parseFloat(record.amount || 0);
                }

                acc.totalMonthlyAmount += parseFloat(record.monthly_amount || 0);
                acc.totalRenewalAmount += parseFloat(record.renewal_amount || 0);
                acc.totalAccumulatedAmount += parseFloat(record.accumulated_amount || 0);

                return acc;
            }, {
                totalAmount: 0,
                totalMonthlyAmount: 0,
                totalRenewalAmount: 0,
                totalAccumulatedAmount: 0
            });

            // 使用DOM缓存更新统计显示
            DOMCache.get('stat-total-amount').textContent = `¥${totals.totalAmount.toFixed(2)}`;
            DOMCache.get('stat-monthly-amount').textContent = `¥${totals.totalMonthlyAmount.toFixed(2)}`;
            DOMCache.get('stat-renewal-amount').textContent = `¥${totals.totalRenewalAmount.toFixed(2)}`;
            DOMCache.get('stat-accumulated-amount').textContent = `¥${totals.totalAccumulatedAmount.toFixed(2)}`;
        }

        // 计算递减记账在指定月份的历史剩余金额
        function calculateHistoricalRemainingAmount(record, viewMonth) {
            const originalAmount = parseFloat(record.amount || 0);
            const accumulatedAmount = parseFloat(record.accumulated_amount || 0);

            // 历史剩余金额 = 原始金额 - 截止到查看月份的累计金额
            const historicalRemainingAmount = originalAmount - accumulatedAmount;

            // 确保不为负数
            return Math.max(0, historicalRemainingAmount);
        }

        async function toggleRecord(recordId) {
            try {
                const currentMonth = new Date().toISOString().substring(0, 7);

                // 根据查看的月份决定使用哪个API
                let action, body;
                if (currentViewMonth === currentMonth) {
                    // 当前月份：使用toggle_record（直接修改记录状态，支持递减功能）
                    action = 'toggle_record';
                    body = `action=${action}&record_id=${recordId}`;
                } else {
                    // 其他月份：使用toggle_record_monthly（管理月份状态）
                    action = 'toggle_record_monthly';
                    body = `action=${action}&record_id=${recordId}&view_month=${currentViewMonth}`;
                }

                // 使用请求去重，防止重复点击
                const response = await deduplicator.request(`api_direct.php`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/x-www-form-urlencoded'
                    },
                    body: body
                });

                const data = await response.json();

                if (data.success) {
                    loadRecords(); // 重新加载记录
                } else {
                    showMessage(data.error || '状态更新失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
            }
        }

        function showAddForm() {
            editingRecordId = null;
            document.getElementById('form-title').textContent = '添加记录';
            document.getElementById('accumulated-amount-display').style.display = 'none';

            // 隐藏创建时间显示
            const createdTimeDisplay = document.getElementById('created-time-display');
            if (createdTimeDisplay) {
                createdTimeDisplay.style.display = 'none';
            }

            // 隐藏账本选择器下拉选项
            hideBookOptionsDropdown();
            // 隐藏续期时间选择器下拉选项
            hideRenewalOptionsDropdown();

            resetRecordForm();
            document.getElementById('add-form-modal').style.display = 'flex';
        }

        function hideAddForm() {
            document.getElementById('add-form-modal').style.display = 'none';
            editingRecordId = null;
            resetRecordForm();
        }

        async function submitRecord(event) {
            event.preventDefault();

            if (!currentBook && !editingRecordId) {
                showMessage('请先选择账本', true);
                return;
            }

            // 获取表单数据
            const date = document.getElementById('record-date').value;
            const name = document.getElementById('record-name').value;
            const amount = document.getElementById('record-amount').value;
            const monthlyAmount = document.getElementById('record-monthly-amount').value;
            const renewalTime = document.getElementById('record-renewal-time').value;
            const renewalAmount = document.getElementById('record-renewal-amount').value;
            const remark = document.getElementById('record-remark').value;
            const accumulatedAmount = document.getElementById('record-accumulated-amount').value;
            const isDecreasing = document.getElementById('record-is-decreasing').checked;

            // 验证必填字段（续期金额改为可选）
            if (!date || !name || !amount || !monthlyAmount || !renewalTime) {
                showMessage('请填写所有必填字段', true);
                return;
            }

            // 续期金额如果不填默认为0
            const finalRenewalAmount = renewalAmount || '0';

            const formData = new FormData();

            if (editingRecordId) {
                // 编辑模式
                formData.append('action', 'update_record');
                formData.append('record_id', editingRecordId);
            } else {
                // 添加模式
                formData.append('action', 'add_record');
                formData.append('book_id', currentBook.id);
            }

            formData.append('date', date);
            formData.append('name', name);
            formData.append('amount', amount);
            formData.append('monthly_amount', monthlyAmount);
            formData.append('renewal_time', renewalTime);
            formData.append('renewal_amount', finalRenewalAmount);
            formData.append('remark', remark);
            formData.append('is_decreasing', isDecreasing ? '1' : '0');

            // 编辑模式时包含累计金额
            if (editingRecordId && accumulatedAmount !== '') {
                formData.append('accumulated_amount', accumulatedAmount);
            }

            try {
                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    hideAddForm();
                    loadRecords();
                    showMessage(editingRecordId ? '记录更新成功！' : '记录添加成功！');
                } else {
                    showMessage(data.error || (editingRecordId ? '更新记录失败' : '添加记录失败'), true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
            }
        }

        function resetRecordForm() {
            document.getElementById('record-date').value = new Date().toISOString().split('T')[0];
            document.getElementById('record-name').value = '';
            document.getElementById('record-amount').value = '';
            document.getElementById('record-monthly-amount').value = '';
            document.getElementById('record-renewal-time').value = '';
            document.getElementById('record-renewal-amount').value = '';
            document.getElementById('record-remark').value = '';
            document.getElementById('record-accumulated-amount').value = '';

            // 重置续期时间选择器显示
            const selectedText = document.getElementById('selected-renewal-text');
            if (selectedText) {
                selectedText.textContent = '请选择';
                selectedText.style.color = '#999';
            }

            // 重置下次续期时间显示
            const nextRenewalDisplay = document.getElementById('next-renewal-date');
            if (nextRenewalDisplay) {
                nextRenewalDisplay.textContent = '下次续期: --';
            }

            // 重置递减模式
            document.getElementById('record-is-decreasing').checked = false;
            toggleDecreasingMode();
        }

        function toggleDecreasingMode() {
            const isDecreasing = document.getElementById('record-is-decreasing').checked;
            const decreasingInfo = document.getElementById('decreasing-info');

            if (isDecreasing) {
                decreasingInfo.style.display = 'block';
            } else {
                decreasingInfo.style.display = 'none';
            }
        }

        // 全局变量用于编辑
        let editingRecordId = null;



        function editRecord(recordId) {
            const record = records.find(r => r.id == recordId);
            if (!record) {
                showMessage('记录不存在', true);
                return;
            }

            // 检查是否是递减记账，如果是则显示确认弹窗
            if (record.is_decreasing == 1) {
                showDecreasingEditConfirm(recordId, record);
                return;
            }

            // 普通记账直接编辑
            proceedWithEdit(recordId, record);
        }

        function proceedWithEdit(recordId, record) {
            editingRecordId = recordId;

            // 隐藏账本选择器下拉选项
            hideBookOptionsDropdown();
            // 隐藏续期时间选择器下拉选项
            hideRenewalOptionsDropdown();

            // 设置表单标题和显示累计金额
            document.getElementById('form-title').textContent = '编辑记录';
            document.getElementById('accumulated-amount-display').style.display = 'block';

            // 显示创建时间
            const createdTimeDisplay = document.getElementById('created-time-display');
            const recordCreatedTime = document.getElementById('record-created-time');
            if (createdTimeDisplay && recordCreatedTime) {
                createdTimeDisplay.style.display = 'block';
                recordCreatedTime.textContent = formatRecordCreateTime(record.created_at);
            }

            // 填充表单数据
            document.getElementById('record-date').value = record.date || '';
            document.getElementById('record-name').value = record.name || '';
            document.getElementById('record-amount').value = record.amount || '';
            document.getElementById('record-monthly-amount').value = record.monthly_amount || '';
            document.getElementById('record-renewal-time').value = record.renewal_time || '';
            document.getElementById('record-renewal-amount').value = record.renewal_amount || '';
            document.getElementById('record-remark').value = record.remark || '';
            document.getElementById('record-accumulated-amount').value = record.accumulated_amount || '';

            // 设置递减模式
            document.getElementById('record-is-decreasing').checked = record.is_decreasing == 1;
            toggleDecreasingMode();

            // 设置续期时间选择器显示值
            const selectedText = document.getElementById('selected-renewal-text');
            if (selectedText) {
                if (record.renewal_time) {
                    selectedText.textContent = record.renewal_time;
                    selectedText.style.color = '#333';
                } else {
                    selectedText.textContent = '请选择';
                    selectedText.style.color = '#999';
                }
            }

            // 显示弹窗
            document.getElementById('add-form-modal').style.display = 'flex';

            // 更新下次续期时间显示
            setTimeout(() => {
                updateNextRenewalDate();
            }, 100);
        }

        async function deleteRecord(recordId) {
            if (!confirm('确定要删除这条记录吗？删除后无法恢复。')) {
                return;
            }

            try {
                const formData = new FormData();
                formData.append('action', 'delete_record');
                formData.append('record_id', recordId);

                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showMessage('记录删除成功！');
                    loadRecords();
                } else {
                    showMessage(data.error || '删除记录失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
            }
        }

        function showChangePassword() {
            // 隐藏账本选择器下拉选项
            hideBookOptionsDropdown();

            document.getElementById('change-password-modal').style.display = 'flex';
            // 清空表单
            document.getElementById('current-password').value = '';
            document.getElementById('new-password').value = '';
            document.getElementById('confirm-password').value = '';
        }

        function hideChangePassword() {
            document.getElementById('change-password-modal').style.display = 'none';
        }

        async function changePassword(event) {
            event.preventDefault();

            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            // 验证输入不为空
            if (!currentPassword || !newPassword || !confirmPassword) {
                showMessage('请填写所有密码字段', true);
                return;
            }

            // 验证新密码和确认密码是否一致
            if (newPassword !== confirmPassword) {
                showMessage('新密码和确认密码不一致', true);
                return;
            }

            // 验证新密码长度
            if (newPassword.length < 6) {
                showMessage('新密码至少需要6位', true);
                return;
            }

            // 验证token存在
            if (!token) {
                showMessage('请先登录', true);
                return;
            }

            try {
                const formData = new FormData();
                formData.append('action', 'change_password');
                formData.append('current_password', currentPassword);
                formData.append('new_password', newPassword);

                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    hideChangePassword();
                    showMessage('密码修改成功！');
                } else {
                    let errorMsg = data.error || '密码修改失败';
                    if (response.status === 400) {
                        errorMsg = `请求错误 (400): ${errorMsg}`;
                    } else if (response.status === 401) {
                        errorMsg = `认证失败 (401): ${errorMsg}`;
                    }
                    showMessage(errorMsg, true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
            }
        }

        function logout() {
            localStorage.removeItem('token');
            token = '';
            currentBook = null;
            books = [];
            records = [];

            // 清除消息显示
            const messageEl = document.getElementById('message');
            messageEl.textContent = '';
            messageEl.style.display = 'none';

            // 重置表单
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';

            // 先隐藏主页面
            document.getElementById('main-page').style.display = 'none';

            // 强制重新设置登录页面的样式
            const loginPage = document.getElementById('login-page');
            loginPage.style.display = 'flex';
            loginPage.style.alignItems = 'center';
            loginPage.style.justifyContent = 'center';
            loginPage.style.minHeight = '100vh';
            loginPage.style.padding = '20px';

            // 重置页面滚动位置到顶部，确保登录界面居中显示
            window.scrollTo(0, 0);
            document.body.scrollTop = 0; // 兼容一些浏览器
            document.documentElement.scrollTop = 0; // 兼容一些浏览器
        }

        // 账本管理函数
        function showAddBookForm() {
            // 隐藏账本选择器下拉选项
            hideBookOptionsDropdown();

            document.getElementById('add-book-modal').style.display = 'flex';
            // 清空表单
            document.getElementById('book-name').value = '';
            document.getElementById('book-description').value = '';
        }

        function hideAddBookForm() {
            document.getElementById('add-book-modal').style.display = 'none';
        }

        async function submitBook(event) {
            event.preventDefault();

            const name = document.getElementById('book-name').value;
            const description = document.getElementById('book-description').value;

            if (!name.trim()) {
                showMessage('请输入账本名称', true);
                return;
            }

            try {
                const formData = new FormData();
                formData.append('action', 'add_book');
                formData.append('name', name);
                formData.append('description', description);

                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    hideAddBookForm();
                    showMessage('账本创建成功！');
                    loadBooks(); // 重新加载账本列表
                } else {
                    showMessage(data.error || '创建账本失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
            }
        }

        async function deleteBook(bookId, bookName) {
            if (!confirm(`确定要删除账本"${bookName}"吗？\n\n删除后该账本下的所有记录也会被删除，此操作不可恢复！`)) {
                return;
            }

            try {
                const formData = new FormData();
                formData.append('action', 'delete_book');
                formData.append('book_id', bookId);

                const response = await fetch('api_direct.php', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    showMessage('账本删除成功！');
                    loadBooks(); // 重新加载账本列表

                    // 如果删除的是当前选中的账本，返回账本列表页面
                    if (currentBook && currentBook.id == bookId) {
                        showBooksList();
                    }
                } else {
                    showMessage(data.error || '删除账本失败', true);
                }
            } catch (error) {
                showMessage('网络错误，请重试', true);
            }
        }

        // 新增：下拉框悬浮body定位与关闭
        function showBookOptionsDropdown() {
            const customSelector = document.getElementById('custom-book-selector');
            const bookOptions = document.getElementById('book-options');
            if (!customSelector || !bookOptions) return;
            // 计算customSelector在页面中的位置
            const rect = customSelector.getBoundingClientRect();
            bookOptions.style.position = 'absolute';
            bookOptions.style.left = rect.left + 'px';
            bookOptions.style.top = (rect.bottom + window.scrollY) + 'px';
            bookOptions.style.width = rect.width + 'px';
            bookOptions.style.zIndex = 9999;
            bookOptions.style.display = 'block';
            // 移动到body下
            document.body.appendChild(bookOptions);
        }
        function hideBookOptionsDropdown() {
            const bookOptions = document.getElementById('book-options');
            if (bookOptions) {
                bookOptions.style.display = 'none';
                // 确保重置 z-index，避免影响其他元素
                bookOptions.style.zIndex = '9999';
            }
        }

        // 续期时间选择器相关函数
        function initRenewalSelector() {
            const renewalOptions = [
                { value: '二个月', label: '⏰ 二个月', icon: '📅' },
                { value: '三个月', label: '⏰ 三个月', icon: '📅' },
                { value: '六个月', label: '⏰ 六个月', icon: '📅' },
                { value: '永久', label: '♾️ 永久', icon: '🔒' }
            ];

            // 生成选项
            const optionsContainer = document.getElementById('renewal-options');
            if (optionsContainer) {
                optionsContainer.innerHTML = '';
                renewalOptions.forEach(option => {
                    const optionEl = document.createElement('div');
                    optionEl.style.cssText = 'padding: 10px 12px; cursor: pointer; border-bottom: 1px solid #f0f0f0; transition: background-color 0.2s; display: flex; align-items: center; gap: 8px;';
                    optionEl.innerHTML = `<span style="font-size: 14px;">${option.icon}</span><span>${option.value}</span>`;
                    optionEl.onclick = () => selectRenewalTime(option.value, option.label);

                    // 悬停效果
                    optionEl.onmouseenter = () => {
                        optionEl.style.backgroundColor = '#f5f5f5';
                    };
                    optionEl.onmouseleave = () => {
                        optionEl.style.backgroundColor = '';
                    };

                    optionsContainer.appendChild(optionEl);
                });
            }

            // 添加点击事件监听
            document.addEventListener('click', function(e) {
                const customSelector = document.getElementById('custom-renewal-selector');
                const renewalOptions = document.getElementById('renewal-options');
                if (!customSelector || !renewalOptions) return;

                if (customSelector.contains(e.target)) {
                    if (renewalOptions.style.display === 'none' || renewalOptions.style.display === '') {
                        showRenewalOptionsDropdown();
                    } else {
                        hideRenewalOptionsDropdown();
                    }
                } else {
                    hideRenewalOptionsDropdown();
                }
            });
        }

        function showRenewalOptionsDropdown() {
            const customSelector = document.getElementById('custom-renewal-selector');
            const renewalOptions = document.getElementById('renewal-options');
            if (!customSelector || !renewalOptions) return;

            // 计算位置
            const rect = customSelector.getBoundingClientRect();
            renewalOptions.style.position = 'absolute';
            renewalOptions.style.left = rect.left + 'px';
            renewalOptions.style.top = (rect.bottom + window.scrollY) + 'px';
            renewalOptions.style.width = rect.width + 'px';
            renewalOptions.style.zIndex = 10001;
            renewalOptions.style.display = 'block';

            // 添加边框高亮效果
            customSelector.style.borderColor = '#1890ff';
            customSelector.style.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.2)';
        }

        function hideRenewalOptionsDropdown() {
            const renewalOptions = document.getElementById('renewal-options');
            const customSelector = document.getElementById('custom-renewal-selector');
            if (renewalOptions) {
                renewalOptions.style.display = 'none';
            }
            if (customSelector) {
                customSelector.style.borderColor = '#ddd';
                customSelector.style.boxShadow = 'none';
            }
        }

        function selectRenewalTime(value, label) {
            const selectedText = document.getElementById('selected-renewal-text');
            const hiddenInput = document.getElementById('record-renewal-time');

            if (selectedText && hiddenInput) {
                selectedText.textContent = value;
                selectedText.style.color = '#333';
                hiddenInput.value = value;
                hideRenewalOptionsDropdown();

                // 触发下次续期时间计算
                updateNextRenewalDate();
            }
        }

        // 全局函数用于更新下次续期时间
        function updateNextRenewalDate() {
            const dateInput = document.getElementById('record-date');
            const renewalSelector = document.getElementById('selected-renewal-text');
            const nextRenewalDisplay = document.getElementById('next-renewal-date');

            if (!dateInput || !renewalSelector || !nextRenewalDisplay) return;

            const currentDate = dateInput.value;
            const renewalText = renewalSelector.textContent;

            if (!currentDate || renewalText === '请选择') {
                nextRenewalDisplay.textContent = '下次续期: --';
                return;
            }

            try {
                const date = new Date(currentDate);
                let nextDate = new Date(date);

                // 根据续期时间计算下次续期日期
                if (renewalText.includes('二个月')) {
                    nextDate.setMonth(nextDate.getMonth() + 2);
                } else if (renewalText.includes('三个月')) {
                    nextDate.setMonth(nextDate.getMonth() + 3);
                } else if (renewalText.includes('六个月')) {
                    nextDate.setMonth(nextDate.getMonth() + 6);
                } else if (renewalText.includes('永久')) {
                    nextRenewalDisplay.textContent = '下次续期: 永久有效';
                    return;
                } else {
                    nextRenewalDisplay.textContent = '下次续期: --';
                    return;
                }

                // 格式化日期显示
                const year = nextDate.getFullYear();
                const month = String(nextDate.getMonth() + 1).padStart(2, '0');
                const day = String(nextDate.getDate()).padStart(2, '0');

                nextRenewalDisplay.textContent = `下次续期: ${year}-${month}-${day}`;
            } catch (error) {
                nextRenewalDisplay.textContent = '下次续期: --';
            }
        }
        // 初始化月份导航
        function initMonthNavigation() {
            const prevBtn = document.getElementById('prev-month-btn');
            const nextBtn = document.getElementById('next-month-btn');

            if (prevBtn) {
                prevBtn.addEventListener('click', () => switchMonth(-1));
            }
            if (nextBtn) {
                nextBtn.addEventListener('click', () => switchMonth(1));
            }

            updateMonthDisplay();
        }

        // 切换月份
        function switchMonth(direction) {
            const currentDate = new Date(currentViewMonth + '-01');
            currentDate.setMonth(currentDate.getMonth() + direction);

            // 限制查看范围：前后12个月
            const now = new Date();
            const minDate = new Date(now.getFullYear() - 1, now.getMonth(), 1);
            const maxDate = new Date(now.getFullYear() + 1, now.getMonth(), 1);

            if (currentDate >= minDate && currentDate <= maxDate) {
                currentViewMonth = currentDate.toISOString().slice(0, 7);
                updateMonthDisplay();

                // 立即加载记录，不使用防抖
                if (currentBook) {
                    loadRecords();
                }
            }
        }

        // 更新月份显示
        function updateMonthDisplay() {
            const currentMonthDisplay = document.getElementById('current-month-display');
            const headerMonthIndicator = document.getElementById('header-month-indicator');
            const prevMonthText = document.getElementById('prev-month-text');
            const nextMonthText = document.getElementById('next-month-text');

            const viewDate = new Date(currentViewMonth + '-01');
            const currentDate = new Date();
            const currentMonthStr = currentDate.toISOString().slice(0, 7);

            // 格式化显示当前查看月份
            const year = viewDate.getFullYear();
            const month = viewDate.getMonth() + 1;
            const monthText = `${year}年${month}月`;

            // 更新底部月份导航显示
            if (currentMonthDisplay) {
                currentMonthDisplay.textContent = monthText;
            }

            // 更新头部月份标识显示
            if (headerMonthIndicator) {
                headerMonthIndicator.textContent = `📅 ${monthText}`;
            }

            // 更新按钮文字
            const prevDate = new Date(viewDate);
            prevDate.setMonth(prevDate.getMonth() - 1);
            const nextDate = new Date(viewDate);
            nextDate.setMonth(nextDate.getMonth() + 1);

            if (prevMonthText) {
                prevMonthText.textContent = `${prevDate.getMonth() + 1}月`;
            }
            if (nextMonthText) {
                nextMonthText.textContent = `${nextDate.getMonth() + 1}月`;
            }

            // 根据是否为当前月调整样式
            let backgroundStyle, boxShadowStyle;
            if (currentViewMonth === currentMonthStr) {
                // 当前月份 - 粉红色
                backgroundStyle = 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)';
                boxShadowStyle = '0 2px 8px rgba(245, 87, 108, 0.3)';
            } else if (currentViewMonth < currentMonthStr) {
                // 历史月份 - 蓝色
                backgroundStyle = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                boxShadowStyle = '0 2px 8px rgba(102, 126, 234, 0.3)';
            } else {
                // 未来月份 - 青色
                backgroundStyle = 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)';
                boxShadowStyle = '0 2px 8px rgba(79, 172, 254, 0.3)';
            }

            // 应用样式到底部月份导航
            if (currentMonthDisplay) {
                currentMonthDisplay.style.background = backgroundStyle;
                currentMonthDisplay.style.boxShadow = boxShadowStyle;
            }

            // 应用样式到头部月份标识
            if (headerMonthIndicator) {
                headerMonthIndicator.style.background = backgroundStyle;
                headerMonthIndicator.style.boxShadow = boxShadowStyle;
            }
        }

        // 月份选择器功能
        function showMonthPicker() {
            const modal = document.getElementById('month-picker-modal');
            if (!modal) return;

            // 初始化选择器状态
            const currentDate = new Date(currentViewMonth + '-01');
            pickerYear = currentDate.getFullYear();
            pickerMonth = currentDate.getMonth() + 1;
            selectedMonth = pickerMonth;

            // 更新显示
            updatePickerDisplay();
            generateMonthGrid();

            // 显示弹窗
            modal.style.display = 'flex';
        }

        function hideMonthPicker() {
            const modal = document.getElementById('month-picker-modal');
            if (modal) {
                modal.style.display = 'none';
            }
        }

        function changePickerYear(direction) {
            pickerYear += direction;

            // 限制年份范围（当前年份前后5年）
            const currentYear = new Date().getFullYear();
            pickerYear = Math.max(currentYear - 5, Math.min(currentYear + 5, pickerYear));

            updatePickerDisplay();
        }

        function updatePickerDisplay() {
            const yearEl = document.getElementById('picker-year');
            if (yearEl) {
                yearEl.textContent = pickerYear;
            }
        }

        function generateMonthGrid() {
            const monthGrid = document.getElementById('month-grid');
            if (!monthGrid) return;

            const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月',
                              '7月', '8月', '9月', '10月', '11月', '12月'];

            monthGrid.innerHTML = '';

            monthNames.forEach((monthName, index) => {
                const monthNum = index + 1;
                const button = document.createElement('button');
                button.textContent = monthName;
                button.onclick = () => selectMonth(monthNum);

                // 样式设置
                const isSelected = monthNum === selectedMonth;
                const isCurrent = pickerYear === new Date().getFullYear() && monthNum === new Date().getMonth() + 1;

                let buttonStyle = `
                    padding: 12px 8px;
                    border: none;
                    border-radius: 8px;
                    cursor: pointer;
                    font-weight: 600;
                    font-size: 13px;
                    transition: all 0.2s ease;
                    text-align: center;
                `;

                if (isSelected) {
                    buttonStyle += `
                        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                        color: white;
                        box-shadow: 0 2px 8px rgba(245, 87, 108, 0.3);
                        transform: scale(1.05);
                    `;
                } else if (isCurrent) {
                    buttonStyle += `
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
                    `;
                } else {
                    buttonStyle += `
                        background: #f8f9fa;
                        color: #333;
                        border: 1px solid #e9ecef;
                    `;
                }

                button.style.cssText = buttonStyle;

                // 悬停效果
                button.onmouseover = function() {
                    if (!isSelected) {
                        this.style.background = '#e9ecef';
                        this.style.transform = 'scale(1.02)';
                    }
                };

                button.onmouseout = function() {
                    if (!isSelected) {
                        if (isCurrent) {
                            this.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
                        } else {
                            this.style.background = '#f8f9fa';
                        }
                        this.style.transform = 'scale(1)';
                    }
                };

                monthGrid.appendChild(button);
            });
        }

        function selectMonth(month) {
            selectedMonth = month;
            generateMonthGrid(); // 重新生成网格以更新选中状态
        }

        function confirmMonthPicker() {
            if (selectedMonth) {
                // 构造新的月份字符串
                const newMonth = `${pickerYear}-${selectedMonth.toString().padStart(2, '0')}`;

                // 检查是否在允许的范围内
                const now = new Date();
                const minDate = new Date(now.getFullYear() - 1, now.getMonth(), 1);
                const maxDate = new Date(now.getFullYear() + 1, now.getMonth(), 1);
                const selectedDate = new Date(pickerYear, selectedMonth - 1, 1);

                if (selectedDate >= minDate && selectedDate <= maxDate) {
                    // 更新当前查看月份
                    currentViewMonth = newMonth;

                    // 更新显示
                    updateMonthDisplay();

                    // 重新加载记录
                    if (currentBook) {
                        loadRecords();
                    }

                    // 关闭弹窗
                    hideMonthPicker();
                } else {
                    alert('只能查看前后12个月的数据');
                }
            }
        }

        // 点击弹窗外部关闭
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('month-picker-modal');
            if (modal && e.target === modal) {
                hideMonthPicker();
            }
        });

        // 递减记账编辑确认功能
        function showDecreasingEditConfirm(recordId, record) {
            const modal = document.getElementById('decreasing-edit-confirm-modal');
            if (!modal) return;

            // 保存待编辑的记录信息
            pendingDecreasingEdit = { recordId, record };

            // 填充确认弹窗的信息
            const nameEl = document.getElementById('confirm-record-name');
            const amountEl = document.getElementById('confirm-record-amount');
            const monthlyEl = document.getElementById('confirm-monthly-amount');

            if (nameEl) nameEl.textContent = record.name || '未命名';
            if (amountEl) amountEl.textContent = `¥${parseFloat(record.amount || 0).toFixed(2)}`;
            if (monthlyEl) monthlyEl.textContent = `¥${parseFloat(record.monthly_amount || 0).toFixed(2)}`;

            // 显示弹窗
            modal.style.display = 'flex';
        }

        function hideDecreasingEditConfirm() {
            const modal = document.getElementById('decreasing-edit-confirm-modal');
            if (modal) {
                modal.style.display = 'none';
            }
            pendingDecreasingEdit = null;
        }

        function cancelDecreasingEdit() {
            hideDecreasingEditConfirm();
        }

        function confirmDecreasingEdit() {
            if (pendingDecreasingEdit) {
                const { recordId, record } = pendingDecreasingEdit;
                hideDecreasingEditConfirm();

                // 继续编辑流程
                proceedWithEdit(recordId, record);
            }
        }

        // 点击弹窗外部关闭递减确认弹窗
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('decreasing-edit-confirm-modal');
            if (modal && e.target === modal) {
                hideDecreasingEditConfirm();
            }
        });
    </script>
</body>
</html>
