<?php
/**
 * 备份管理API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 备份目录
$backupDir = __DIR__ . '/Backup';

// 获取操作类型
$action = $_GET['action'] ?? ($_POST['action'] ?? null);
if (!$action) {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? null;
}

try {
    switch ($action) {
        case 'list_backups':
            listBackups();
            break;

        case 'create_backup':
            createBackup();
            break;

        case 'delete_backup':
            deleteBackup();
            break;

        case 'download':
            downloadBackup();
            break;

        case 'restore_backup':
            restoreBackup();
            break;

        default:
            throw new Exception('无效的操作类型');
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 列出所有备份
 */
function listBackups() {
    global $backupDir;

    if (!is_dir($backupDir)) {
        echo json_encode([
            'success' => true,
            'data' => []
        ]);
        return;
    }

    $backups = [];
    $items = scandir($backupDir);

    foreach ($items as $item) {
        if ($item === '.' || $item === '..') continue;

        $itemPath = $backupDir . '/' . $item;

        if (is_dir($itemPath)) {
            // 目录备份
            $size = getDirSize($itemPath);
            $backups[] = [
                'name' => $item,
                'type' => 'directory',
                'date' => date('Y-m-d H:i:s', filemtime($itemPath)),
                'size' => formatBytes($size),
                'raw_size' => $size
            ];
        } elseif (pathinfo($item, PATHINFO_EXTENSION) === 'zip') {
            // ZIP备份
            $backups[] = [
                'name' => $item,
                'type' => 'zip',
                'date' => date('Y-m-d H:i:s', filemtime($itemPath)),
                'size' => formatBytes(filesize($itemPath)),
                'raw_size' => filesize($itemPath)
            ];
        }
    }

    // 按日期排序（最新的在前）
    usort($backups, function($a, $b) {
        return strtotime($b['date']) - strtotime($a['date']);
    });

    echo json_encode([
        'success' => true,
        'data' => $backups
    ]);
}

/**
 * 创建备份
 */
function createBackup() {
    // 执行快速备份脚本
    $output = [];
    $returnCode = 0;

    exec('cd ' . escapeshellarg(__DIR__) . ' && php quick_backup.php 2>&1', $output, $returnCode);

    if ($returnCode === 0) {
        echo json_encode([
            'success' => true,
            'message' => '备份创建成功',
            'output' => implode("\n", $output)
        ]);
    } else {
        throw new Exception('备份创建失败: ' . implode("\n", $output));
    }
}

/**
 * 删除备份
 */
function deleteBackup() {
    global $backupDir;

    $input = json_decode(file_get_contents('php://input'), true);
    $backupName = $input['backup_name'] ?? $_POST['backup_name'] ?? null;

    if (!$backupName) {
        throw new Exception('未指定备份名称');
    }

    $backupPath = $backupDir . '/' . $backupName;

    // 安全检查
    if (!file_exists($backupPath) || strpos(realpath($backupPath), realpath($backupDir)) !== 0) {
        throw new Exception('备份不存在或路径无效');
    }

    if (is_dir($backupPath)) {
        removeDirectory($backupPath);
    } else {
        unlink($backupPath);
    }

    echo json_encode([
        'success' => true,
        'message' => '备份删除成功'
    ]);
}

/**
 * 下载备份
 */
function downloadBackup() {
    global $backupDir;

    $backupName = $_GET['backup'] ?? null;

    if (!$backupName) {
        throw new Exception('未指定备份名称');
    }

    $backupPath = $backupDir . '/' . $backupName;

    // 安全检查
    if (!file_exists($backupPath) || strpos(realpath($backupPath), realpath($backupDir)) !== 0) {
        throw new Exception('备份不存在或路径无效');
    }

    if (is_dir($backupPath)) {
        // 如果是目录，创建临时压缩文件
        $tempDir = sys_get_temp_dir();
        $tarFile = $tempDir . '/' . $backupName . '.tar.gz';

        // 检查临时目录权限
        if (!is_writable($tempDir)) {
            // 使用项目目录作为临时目录
            $tempDir = __DIR__ . '/temp';
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }
            $tarFile = $tempDir . '/' . $backupName . '.tar.gz';
        }

        // 尝试使用ZipArchive（如果可用）
        if (class_exists('ZipArchive')) {
            $zipFile = $tempDir . '/' . $backupName . '.zip';
            $zip = new ZipArchive();
            if ($zip->open($zipFile, ZipArchive::CREATE) === TRUE) {
                addDirectoryToZip($zip, $backupPath, '');
                $zip->close();

                // 下载ZIP文件
                header('Content-Type: application/zip');
                header('Content-Disposition: attachment; filename="' . $backupName . '.zip"');
                header('Content-Length: ' . filesize($zipFile));
                readfile($zipFile);

                // 删除临时文件
                unlink($zipFile);
                exit;
            }
        }

        // 备用方案：使用tar命令
        $command = sprintf(
            'cd %s && tar -czf %s %s 2>&1',
            escapeshellarg(dirname($backupPath)),
            escapeshellarg($tarFile),
            escapeshellarg(basename($backupPath))
        );

        exec($command, $output, $returnCode);

        if ($returnCode === 0 && file_exists($tarFile)) {
            // 下载tar.gz文件
            header('Content-Type: application/gzip');
            header('Content-Disposition: attachment; filename="' . $backupName . '.tar.gz"');
            header('Content-Length: ' . filesize($tarFile));
            readfile($tarFile);

            // 删除临时文件
            unlink($tarFile);
            exit;
        }

        // 最后的备用方案：创建简单的文本文件列表
        $listFile = $tempDir . '/' . $backupName . '_filelist.txt';
        $fileList = "备份目录: $backupName\n";
        $fileList .= "创建时间: " . date('Y-m-d H:i:s') . "\n\n";
        $fileList .= "文件列表:\n";

        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($backupPath, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            $relativePath = substr($file->getPathname(), strlen($backupPath) + 1);
            $fileList .= $relativePath . " (" . formatBytes($file->getSize()) . ")\n";
        }

        file_put_contents($listFile, $fileList);

        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="' . $backupName . '_filelist.txt"');
        header('Content-Length: ' . filesize($listFile));
        readfile($listFile);

        unlink($listFile);
        exit;
    } else {
        // 直接下载文件
        $mimeType = 'application/octet-stream';
        if (pathinfo($backupName, PATHINFO_EXTENSION) === 'zip') {
            $mimeType = 'application/zip';
        }

        header('Content-Type: ' . $mimeType);
        header('Content-Disposition: attachment; filename="' . basename($backupName) . '"');
        header('Content-Length: ' . filesize($backupPath));
        readfile($backupPath);
        exit;
    }
}

/**
 * 计算目录大小
 */
function getDirSize($dir) {
    $size = 0;
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));

    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $size += $file->getSize();
        }
    }

    return $size;
}

/**
 * 格式化字节大小
 */
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];

    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }

    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * 删除目录
 */
function removeDirectory($dir) {
    if (!is_dir($dir)) return;

    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );

    foreach ($iterator as $file) {
        if ($file->isDir()) {
            rmdir($file->getPathname());
        } else {
            unlink($file->getPathname());
        }
    }

    rmdir($dir);
}

/**
 * 恢复备份
 */
function restoreBackup() {
    global $backupDir;

    $input = json_decode(file_get_contents('php://input'), true);
    $backupName = $input['backup_name'] ?? null;
    $restoreType = $input['restore_type'] ?? 'full';

    if (!$backupName) {
        throw new Exception('未指定备份名称');
    }

    $backupPath = $backupDir . '/' . $backupName;

    // 安全检查
    if (!is_dir($backupPath) || strpos(realpath($backupPath), realpath($backupDir)) !== 0) {
        throw new Exception('备份不存在或路径无效');
    }

    // 数据库配置
    $dbConfig = [
        'host' => 'shuju-mysql.ns-qqzrupud.svc',
        'port' => '3306',
        'name' => 'shuju',
        'user' => 'root',
        'pass' => 'gb5bjqq9'
    ];

    $results = [];

    try {
        // 1. 创建当前状态的备份（安全措施）
        $currentBackupName = 'before_restore_' . date('Y-m-d_H-i-s');
        $currentBackupDir = $backupDir . '/' . $currentBackupName;
        mkdir($currentBackupDir, 0755, true);

        // 备份当前数据库
        if ($restoreType === 'full' || $restoreType === 'database') {
            $currentDbFile = $currentBackupDir . '/current_database.sql';
            $command = sprintf(
                'mysqldump -h%s -P%s -u%s -p%s %s > %s 2>&1',
                escapeshellarg($dbConfig['host']),
                escapeshellarg($dbConfig['port']),
                escapeshellarg($dbConfig['user']),
                escapeshellarg($dbConfig['pass']),
                escapeshellarg($dbConfig['name']),
                escapeshellarg($currentDbFile)
            );
            exec($command, $output, $returnCode);

            if ($returnCode === 0) {
                $results[] = '✓ 当前数据库已备份';
            }
        }

        // 备份当前重要文件
        if ($restoreType === 'full' || $restoreType === 'files') {
            $importantFiles = ['index.html', 'api_direct.php', '.htaccess'];
            foreach ($importantFiles as $file) {
                $sourcePath = __DIR__ . '/' . $file;
                $destPath = $currentBackupDir . '/' . $file;
                if (file_exists($sourcePath)) {
                    copy($sourcePath, $destPath);
                }
            }
            $results[] = '✓ 当前文件已备份';
        }

        // 2. 执行恢复操作
        if ($restoreType === 'full' || $restoreType === 'database') {
            // 恢复数据库
            $sqlFile = $backupPath . '/database.sql';
            if (file_exists($sqlFile)) {
                $command = sprintf(
                    'mysql -h%s -P%s -u%s -p%s %s < %s 2>&1',
                    escapeshellarg($dbConfig['host']),
                    escapeshellarg($dbConfig['port']),
                    escapeshellarg($dbConfig['user']),
                    escapeshellarg($dbConfig['pass']),
                    escapeshellarg($dbConfig['name']),
                    escapeshellarg($sqlFile)
                );

                exec($command, $dbOutput, $dbReturnCode);

                if ($dbReturnCode === 0) {
                    $results[] = '✓ 数据库恢复成功';
                } else {
                    throw new Exception('数据库恢复失败: ' . implode("\n", $dbOutput));
                }
            } else {
                throw new Exception('备份中未找到数据库文件');
            }
        }

        if ($restoreType === 'full' || $restoreType === 'files') {
            // 恢复文件
            $restoredCount = 0;
            $iterator = new RecursiveIteratorIterator(
                new RecursiveDirectoryIterator($backupPath, RecursiveDirectoryIterator::SKIP_DOTS)
            );

            foreach ($iterator as $file) {
                if ($file->isFile() && $file->getFilename() !== 'database.sql' && $file->getFilename() !== 'backup_info.txt') {
                    $relativePath = substr($file->getPathname(), strlen($backupPath) + 1);
                    $destPath = __DIR__ . '/' . $relativePath;

                    // 创建目标目录
                    $destDir = dirname($destPath);
                    if (!is_dir($destDir)) {
                        mkdir($destDir, 0755, true);
                    }

                    if (copy($file->getPathname(), $destPath)) {
                        $restoredCount++;
                    }
                }
            }

            $results[] = "✓ 恢复了 $restoredCount 个文件";
        }

        // 3. 创建恢复信息文件
        $restoreInfo = [
            'restore_time' => date('Y-m-d H:i:s'),
            'backup_name' => $backupName,
            'restore_type' => $restoreType,
            'current_backup' => $currentBackupName,
            'results' => $results
        ];

        file_put_contents(
            $currentBackupDir . '/restore_info.json',
            json_encode($restoreInfo, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)
        );

        echo json_encode([
            'success' => true,
            'message' => '恢复操作完成',
            'results' => $results,
            'current_backup' => $currentBackupName
        ]);

    } catch (Exception $e) {
        // 恢复失败时的清理
        if (isset($currentBackupDir) && is_dir($currentBackupDir)) {
            // 保留失败前的备份，但记录错误
            file_put_contents(
                $currentBackupDir . '/restore_error.log',
                date('Y-m-d H:i:s') . " - 恢复失败: " . $e->getMessage()
            );
        }

        throw $e;
    }
}

/**
 * 添加目录到ZIP
 */
function addDirectoryToZip($zip, $source, $prefix) {
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS)
    );

    foreach ($iterator as $file) {
        $relativePath = $prefix . substr($file->getPathname(), strlen($source) + 1);

        if ($file->isFile()) {
            $zip->addFile($file->getPathname(), $relativePath);
        }
    }
}
?>
