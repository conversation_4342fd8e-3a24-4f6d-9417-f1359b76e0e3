<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    require_once 'backend/api/config.php';

    $action = $_GET['action'] ?? $_POST['action'] ?? 'test';

    switch ($action) {
        case 'test':
            echo json_encode([
                'success' => true,
                'message' => 'API连接正常',
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;

        case 'login':
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';

            if (empty($username) || empty($password)) {
                http_response_code(400);
                echo json_encode(['error' => '用户名和密码不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 查找用户
            $stmt = $pdo->prepare("SELECT id, username, email, password FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch();

            if (!$user || !password_verify($password, $user['password'])) {
                http_response_code(401);
                echo json_encode(['error' => '用户名或密码错误']);
                exit;
            }

            // 生成JWT
            $payload = [
                'user_id' => $user['id'],
                'username' => $user['username'],
                'exp' => time() + (7 * 24 * 60 * 60)
            ];
            $token = JWT::encode($payload);

            echo json_encode([
                'success' => true,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email']
                    ]
                ]
            ]);
            break;

        case 'register':
            $username = $_POST['username'] ?? '';
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';

            if (empty($username) || empty($email) || empty($password)) {
                http_response_code(400);
                echo json_encode(['error' => '用户名、邮箱和密码不能为空']);
                exit;
            }

            if (strlen($password) < 6) {
                http_response_code(400);
                echo json_encode(['error' => '密码至少需要6位']);
                exit;
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                http_response_code(400);
                echo json_encode(['error' => '邮箱格式不正确']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 检查用户名是否已存在
            $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->execute([$username]);
            if ($stmt->fetch()) {
                http_response_code(400);
                echo json_encode(['error' => '用户名已存在']);
                exit;
            }

            // 检查邮箱是否已存在
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            if ($stmt->fetch()) {
                http_response_code(400);
                echo json_encode(['error' => '邮箱已被注册']);
                exit;
            }

            // 创建新用户
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password) VALUES (?, ?, ?)");
            $stmt->execute([$username, $email, $hashedPassword]);

            $userId = $pdo->lastInsertId();

            // 创建默认账本
            $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
            $stmt->execute([$userId, '默认账本', '系统自动创建的默认账本']);

            // 生成JWT
            $payload = [
                'user_id' => $userId,
                'username' => $username,
                'exp' => time() + (7 * 24 * 60 * 60)
            ];
            $token = JWT::encode($payload);

            echo json_encode([
                'success' => true,
                'message' => '注册成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $userId,
                        'username' => $username,
                        'email' => $email
                    ]
                ]
            ]);
            break;

        case 'create_admin':
            $db = new Database();
            $pdo = $db->getConnection();

            // 删除现有admin用户
            $stmt = $pdo->prepare("DELETE FROM users WHERE username = 'admin'");
            $stmt->execute();

            // 创建新的admin用户
            $hashedPassword = password_hash('password', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT INTO users (username, email, password) VALUES (?, ?, ?)");
            $stmt->execute(['admin', '<EMAIL>', $hashedPassword]);

            $userId = $pdo->lastInsertId();

            // 创建默认账本
            $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
            $stmt->execute([$userId, '默认账本', '系统自动创建的默认账本']);

            echo json_encode([
                'success' => true,
                'message' => 'admin用户创建成功',
                'data' => [
                    'user_id' => $userId,
                    'username' => 'admin',
                    'password' => 'password'
                ]
            ]);
            break;

        case 'init_monthly_system':
            $db = new Database();
            $pdo = $db->getConnection();

            // 创建月份状态表
            $createTableSQL = "
                CREATE TABLE IF NOT EXISTS record_monthly_states (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    record_id INT NOT NULL,
                    view_month VARCHAR(7) NOT NULL COMMENT '格式: 2024-05',
                    is_completed TINYINT(1) DEFAULT 0,
                    completed_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_record_month (record_id, view_month),
                    FOREIGN KEY (record_id) REFERENCES records(id) ON DELETE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='记录月份状态表'
            ";

            try {
                $pdo->exec($createTableSQL);
                echo json_encode([
                    'success' => true,
                    'message' => '月份系统初始化成功'
                ]);
            } catch (Exception $e) {
                http_response_code(500);
                echo json_encode([
                    'error' => '月份系统初始化失败: ' . $e->getMessage()
                ]);
            }
            break;

        case 'get_books':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            $stmt = $pdo->prepare("
                SELECT ab.*,
                       COUNT(r.id) as record_count,
                       COALESCE(SUM(r.accumulated_amount), 0) as total_amount
                FROM account_books ab
                LEFT JOIN records r ON ab.id = r.account_book_id
                WHERE ab.user_id = ?
                GROUP BY ab.id
                ORDER BY ab.created_at DESC
            ");
            $stmt->execute([$userId]);
            $books = $stmt->fetchAll();

            echo json_encode([
                'success' => true,
                'data' => $books
            ]);
            break;

        case 'get_records':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $bookId = $_GET['book_id'] ?? '';
            $viewMonth = $_GET['view_month'] ?? date('Y-m'); // 默认当前月

            if (empty($bookId)) {
                http_response_code(400);
                echo json_encode(['error' => '账本ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证账本是否属于当前用户
            $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
            $stmt->execute([$bookId, $userId]);
            if (!$stmt->fetch()) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此账本']);
                exit;
            }

            // 获取记录，并根据查看月份计算状态和累计金额
            $currentMonth = date('Y-m');

            $stmt = $pdo->prepare("
                SELECT r.*,
                       CASE
                           WHEN ? = ? THEN r.is_completed  -- 当前月使用原始状态
                           ELSE COALESCE(rms.is_completed, 0)  -- 其他月份：有月份状态则用月份状态，否则默认为0（未完成）
                       END as current_completed,
                       rms.completed_at as monthly_completed_at
                FROM records r
                LEFT JOIN record_monthly_states rms ON r.id = rms.record_id AND rms.view_month = ?
                WHERE r.account_book_id = ?
                ORDER BY r.created_at DESC
            ");
            $stmt->execute([$viewMonth, $currentMonth, $viewMonth, $bookId]);
            $records = $stmt->fetchAll();

            // 计算累计金额（截止到查看月份）
            foreach ($records as &$record) {
                $record['accumulated_amount'] = calculateAccumulatedAmount($pdo, $record['id'], $viewMonth);
            }

            echo json_encode([
                'success' => true,
                'data' => $records,
                'view_month' => $viewMonth
            ]);
            break;

        case 'add_record':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $bookId = $_POST['book_id'] ?? '';
            $date = $_POST['date'] ?? '';
            $name = $_POST['name'] ?? '';
            $amount = $_POST['amount'] ?? '';
            $monthlyAmount = $_POST['monthly_amount'] ?? '';
            $renewalTime = $_POST['renewal_time'] ?? '';
            $renewalAmount = $_POST['renewal_amount'] ?? '';
            $remark = $_POST['remark'] ?? '';
            $isDecreasing = $_POST['is_decreasing'] ?? '0';

            if (empty($bookId) || empty($date) || empty($name) || empty($amount) || empty($monthlyAmount) || empty($renewalTime)) {
                http_response_code(400);
                echo json_encode(['error' => '必填字段不能为空']);
                exit;
            }

            // 续期金额如果不填默认为0
            if (empty($renewalAmount)) {
                $renewalAmount = 0;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证账本是否属于当前用户
            $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
            $stmt->execute([$bookId, $userId]);
            if (!$stmt->fetch()) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此账本']);
                exit;
            }

            // 初始累计金额为0，只有标记完成时才累计
            $accumulatedAmount = 0;

            // 递减形式的剩余金额初始化为总金额
            $remainingAmount = $isDecreasing == '1' ? $amount : 0;

            // 插入记录
            $stmt = $pdo->prepare("
                INSERT INTO records (account_book_id, date, name, amount, monthly_amount, renewal_time, renewal_amount, accumulated_amount, remark, is_completed, is_decreasing, remaining_amount, is_finished)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, ?, 0)
            ");
            $stmt->execute([
                $bookId, $date, $name, $amount, $monthlyAmount,
                $renewalTime, $renewalAmount, $accumulatedAmount, $remark,
                $isDecreasing, $remainingAmount
            ]);

            $recordId = $pdo->lastInsertId();

            // 获取新创建的记录
            $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
            $stmt->execute([$recordId]);
            $record = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'message' => '记录添加成功',
                'data' => $record
            ]);
            break;

        case 'toggle_record':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            if (empty($recordId)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证记录是否属于当前用户
            $stmt = $pdo->prepare("
                SELECT r.*, ab.user_id
                FROM records r
                JOIN account_books ab ON r.account_book_id = ab.id
                WHERE r.id = ? AND ab.user_id = ?
            ");
            $stmt->execute([$recordId, $userId]);
            $record = $stmt->fetch();

            if (!$record) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此记录']);
                exit;
            }

            // 切换完成状态
            $newStatus = $record['is_completed'] ? 0 : 1;

            // 获取记录的最后操作时间（如果没有则使用创建时间）
            $lastOperationTime = $record['updated_at'] ?? $record['created_at'];
            $lastOperationMonth = date('Y-m', strtotime($lastOperationTime));
            $currentMonth = date('Y-m');

            // 计算新的累计金额
            $newAccumulatedAmount = floatval($record['accumulated_amount']);

            // 初始化递减相关变量
            $newRemainingAmount = floatval($record['remaining_amount']);
            $isFinished = $record['is_finished'];

            // 续期判断函数
            function isRenewalMonth($record) {
                $recordDate = $record['date'];
                $renewalTime = $record['renewal_time'];
                $currentDate = date('Y-m-d');

                // 如果没有续期时间，则不是续期月份
                if (empty($renewalTime) || $renewalTime === '永久') {
                    return false;
                }

                // 解析续期时间
                $monthsToAdd = 0;
                switch ($renewalTime) {
                    case '二个月':
                        $monthsToAdd = 2;
                        break;
                    case '三个月':
                        $monthsToAdd = 3;
                        break;
                    case '六个月':
                        $monthsToAdd = 6;
                        break;
                    default:
                        return false;
                }

                // 计算从记录日期开始的所有续期月份
                $startDate = new DateTime($recordDate);
                $currentDateTime = new DateTime($currentDate);

                // 循环检查是否为续期月份
                $checkDate = clone $startDate;
                while ($checkDate <= $currentDateTime) {
                    $checkDate->add(new DateInterval("P{$monthsToAdd}M"));

                    // 检查当前月份是否为续期月份
                    if ($checkDate->format('Y-m') === $currentDateTime->format('Y-m')) {
                        return true;
                    }

                    // 防止无限循环
                    if ($checkDate->format('Y') > $currentDateTime->format('Y') + 10) {
                        break;
                    }
                }

                return false;
            }

            // 检查是否为已结束的递减记录
            if ($record['is_decreasing'] && $record['is_finished']) {
                http_response_code(400);
                echo json_encode(['error' => '此记账已清零，请编辑或删除此记账']);
                exit;
            }

            if ($newStatus == 1) {
                // 从未完成 → 完成：累计金额增加
                $isRenewalMonth = isRenewalMonth($record);
                $addAmount = $isRenewalMonth ? floatval($record['renewal_amount']) : floatval($record['monthly_amount']);
                $newAccumulatedAmount += $addAmount;

                // 递减形式处理
                if ($record['is_decreasing']) {
                    $newRemainingAmount -= $addAmount;
                    // 如果剩余金额小于等于0，标记为结束
                    if ($newRemainingAmount <= 0) {
                        $isFinished = 1;
                    }
                }
            } else {
                // 从完成 → 未完成：只有当月操作才能减少累计金额
                if ($lastOperationMonth == $currentMonth) {
                    $isRenewalMonth = isRenewalMonth($record);
                    $subtractAmount = $isRenewalMonth ? floatval($record['renewal_amount']) : floatval($record['monthly_amount']);
                    $newAccumulatedAmount = max(0, $newAccumulatedAmount - $subtractAmount);

                    // 递减形式处理
                    if ($record['is_decreasing']) {
                        $newRemainingAmount += $subtractAmount;
                        // 如果剩余金额大于0，取消结束状态
                        if ($newRemainingAmount > 0) {
                            $isFinished = 0;
                        }
                    }
                } else {
                    // 过月锁定，不能减少累计金额
                    http_response_code(400);
                    echo json_encode(['error' => '该记录已过月锁定，无法减少累计金额']);
                    exit;
                }
            }

            // 更新记录，同时更新操作时间
            if ($record['is_decreasing']) {
                $stmt = $pdo->prepare("UPDATE records SET is_completed = ?, accumulated_amount = ?, remaining_amount = ?, is_finished = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$newStatus, $newAccumulatedAmount, $newRemainingAmount, $isFinished, $recordId]);
            } else {
                $stmt = $pdo->prepare("UPDATE records SET is_completed = ?, accumulated_amount = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$newStatus, $newAccumulatedAmount, $recordId]);
            }

            // 获取更新后的记录
            $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
            $stmt->execute([$recordId]);
            $updatedRecord = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'message' => '状态更新成功',
                'data' => $updatedRecord
            ]);
            break;

        case 'update_record':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            $date = $_POST['date'] ?? '';
            $name = $_POST['name'] ?? '';
            $amount = $_POST['amount'] ?? '';
            $monthlyAmount = $_POST['monthly_amount'] ?? '';
            $renewalTime = $_POST['renewal_time'] ?? '';
            $renewalAmount = $_POST['renewal_amount'] ?? '';
            $remark = $_POST['remark'] ?? '';
            $isDecreasing = $_POST['is_decreasing'] ?? '0';

            if (empty($recordId) || empty($date) || empty($name) || empty($amount) || empty($monthlyAmount) || empty($renewalTime)) {
                http_response_code(400);
                echo json_encode(['error' => '必填字段不能为空']);
                exit;
            }

            // 续期金额如果不填默认为0
            if (empty($renewalAmount)) {
                $renewalAmount = 0;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证记录是否属于当前用户
            $stmt = $pdo->prepare("
                SELECT r.*, ab.user_id
                FROM records r
                JOIN account_books ab ON r.account_book_id = ab.id
                WHERE r.id = ? AND ab.user_id = ?
            ");
            $stmt->execute([$recordId, $userId]);
            $record = $stmt->fetch();

            if (!$record) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此记录']);
                exit;
            }

            // 检查是否有累计金额参数
            $accumulatedAmount = $_POST['accumulated_amount'] ?? null;

            // 计算剩余金额（递减形式）
            $remainingAmount = $isDecreasing == '1' ? $amount : 0;

            if ($accumulatedAmount !== null) {
                // 更新记录包含累计金额
                $stmt = $pdo->prepare("
                    UPDATE records
                    SET date = ?, name = ?, amount = ?, monthly_amount = ?, renewal_time = ?, renewal_amount = ?, remark = ?, accumulated_amount = ?, is_decreasing = ?, remaining_amount = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $date, $name, $amount, $monthlyAmount,
                    $renewalTime, $renewalAmount, $remark, $accumulatedAmount,
                    $isDecreasing, $remainingAmount, $recordId
                ]);
            } else {
                // 更新记录不包含累计金额
                $stmt = $pdo->prepare("
                    UPDATE records
                    SET date = ?, name = ?, amount = ?, monthly_amount = ?, renewal_time = ?, renewal_amount = ?, remark = ?, is_decreasing = ?, remaining_amount = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([
                    $date, $name, $amount, $monthlyAmount,
                    $renewalTime, $renewalAmount, $remark,
                    $isDecreasing, $remainingAmount, $recordId
                ]);
            }

            // 获取更新后的记录
            $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
            $stmt->execute([$recordId]);
            $updatedRecord = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'message' => '记录更新成功',
                'data' => $updatedRecord
            ]);
            break;

        case 'delete_record':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            if (empty($recordId)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证记录是否属于当前用户
            $stmt = $pdo->prepare("
                SELECT r.*, ab.user_id
                FROM records r
                JOIN account_books ab ON r.account_book_id = ab.id
                WHERE r.id = ? AND ab.user_id = ?
            ");
            $stmt->execute([$recordId, $userId]);
            $record = $stmt->fetch();

            if (!$record) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此记录']);
                exit;
            }

            // 删除记录
            $stmt = $pdo->prepare("DELETE FROM records WHERE id = ?");
            $stmt->execute([$recordId]);

            echo json_encode([
                'success' => true,
                'message' => '记录删除成功'
            ]);
            break;

        case 'add_book':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';

            if (empty($name)) {
                http_response_code(400);
                echo json_encode(['error' => '账本名称不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 创建账本
            $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
            $stmt->execute([$userId, $name, $description]);

            $bookId = $pdo->lastInsertId();

            // 获取新创建的账本
            $stmt = $pdo->prepare("SELECT * FROM account_books WHERE id = ?");
            $stmt->execute([$bookId]);
            $book = $stmt->fetch();

            echo json_encode([
                'success' => true,
                'message' => '账本创建成功',
                'data' => $book
            ]);
            break;

        case 'delete_book':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $bookId = $_POST['book_id'] ?? '';
            if (empty($bookId)) {
                http_response_code(400);
                echo json_encode(['error' => '账本ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证账本所有权
            $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
            $stmt->execute([$bookId, $userId]);
            if (!$stmt->fetch()) {
                http_response_code(403);
                echo json_encode(['error' => '账本不存在或无权限']);
                exit;
            }

            // 检查是否为用户唯一账本
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM account_books WHERE user_id = ?");
            $stmt->execute([$userId]);
            $count = $stmt->fetch()['count'];

            if ($count <= 1) {
                http_response_code(400);
                echo json_encode(['error' => '不能删除唯一的账本']);
                exit;
            }

            // 删除账本（会级联删除相关记录）
            $stmt = $pdo->prepare("DELETE FROM account_books WHERE id = ?");
            $stmt->execute([$bookId]);

            echo json_encode([
                'success' => true,
                'message' => '账本删除成功'
            ]);
            break;

        case 'change_password':
            // 验证token - 支持多种header格式
            $headers = getallheaders();
            $authHeader = '';

            // 尝试不同的header名称格式
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }

            // 如果还是没有找到，尝试从$_SERVER中获取
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }

            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }

            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';

            if (empty($currentPassword) || empty($newPassword)) {
                http_response_code(400);
                echo json_encode(['error' => '当前密码和新密码不能为空']);
                exit;
            }

            if (strlen($newPassword) < 6) {
                http_response_code(400);
                echo json_encode(['error' => '新密码至少需要6位']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 获取用户信息
            $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch();

            if (!$user) {
                http_response_code(404);
                echo json_encode(['error' => '用户不存在']);
                exit;
            }

            // 验证当前密码
            if (!password_verify($currentPassword, $user['password'])) {
                http_response_code(400);
                echo json_encode(['error' => '当前密码错误']);
                exit;
            }

            // 更新密码
            $hashedNewPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$hashedNewPassword, $userId]);

            echo json_encode([
                'success' => true,
                'message' => '密码修改成功'
            ]);
            break;

        case 'toggle_record_monthly':
            // 验证token
            $headers = getallheaders();
            $authHeader = '';
            foreach (['Authorization', 'authorization', 'HTTP_AUTHORIZATION'] as $headerName) {
                if (isset($headers[$headerName])) {
                    $authHeader = $headers[$headerName];
                    break;
                }
            }
            if (empty($authHeader)) {
                $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
            }
            if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
                http_response_code(401);
                echo json_encode(['error' => '未提供认证token']);
                exit;
            }
            $token = $matches[1];
            try {
                $payload = JWT::decode($token);
                $userId = $payload['user_id'];
            } catch (Exception $e) {
                http_response_code(401);
                echo json_encode(['error' => 'token无效']);
                exit;
            }

            $recordId = $_POST['record_id'] ?? '';
            $viewMonth = $_POST['view_month'] ?? date('Y-m');

            if (empty($recordId)) {
                http_response_code(400);
                echo json_encode(['error' => '记录ID不能为空']);
                exit;
            }

            $db = new Database();
            $pdo = $db->getConnection();

            // 验证记录权限
            $stmt = $pdo->prepare("
                SELECT r.*, ab.user_id
                FROM records r
                JOIN account_books ab ON r.account_book_id = ab.id
                WHERE r.id = ? AND ab.user_id = ?
            ");
            $stmt->execute([$recordId, $userId]);
            $record = $stmt->fetch();

            if (!$record) {
                http_response_code(403);
                echo json_encode(['error' => '无权访问此记录']);
                exit;
            }

            // 获取当前月份状态
            $stmt = $pdo->prepare("
                SELECT * FROM record_monthly_states
                WHERE record_id = ? AND view_month = ?
            ");
            $stmt->execute([$recordId, $viewMonth]);
            $monthlyState = $stmt->fetch();

            $currentMonth = date('Y-m');

            // 根据月份类型获取当前完成状态
            if ($viewMonth === $currentMonth) {
                // 当前月：使用原始记录状态
                $currentCompleted = $record['is_completed'];
            } else {
                // 历史/未来月：使用月份状态，如果没有则默认为0（未完成）
                $currentCompleted = $monthlyState ? $monthlyState['is_completed'] : 0;
            }

            $newCompleted = $currentCompleted ? 0 : 1;

            // 检查是否为已结束的递减记录
            if ($record['is_decreasing'] && $record['is_finished']) {
                http_response_code(400);
                echo json_encode(['error' => '此记账已清零，请编辑或删除此记账']);
                exit;
            }

            // 计算递减逻辑（适用于所有月份的首次完成标记）
            $newAccumulatedAmount = floatval($record['accumulated_amount']);
            $newRemainingAmount = floatval($record['remaining_amount']);
            $isFinished = $record['is_finished'];

            if ($newCompleted == 1) {
                // 从未完成 → 完成：执行递减逻辑
                $isRenewalMonth = isRenewalMonthForDate($record, $viewMonth);
                $addAmount = $isRenewalMonth ? floatval($record['renewal_amount']) : floatval($record['monthly_amount']);
                $newAccumulatedAmount += $addAmount;

                // 递减形式处理（任何月份的首次完成都要递减）
                if ($record['is_decreasing']) {
                    $newRemainingAmount -= $addAmount;
                    // 如果剩余金额小于等于0，标记为结束
                    if ($newRemainingAmount <= 0) {
                        $newRemainingAmount = 0; // 确保不为负数
                        $isFinished = 1;
                    }
                }
            } else {
                // 从完成 → 未完成：恢复递减逻辑
                $isRenewalMonth = isRenewalMonthForDate($record, $viewMonth);
                $subtractAmount = $isRenewalMonth ? floatval($record['renewal_amount']) : floatval($record['monthly_amount']);
                $newAccumulatedAmount = max(0, $newAccumulatedAmount - $subtractAmount);

                // 递减形式处理（恢复递减状态）
                if ($record['is_decreasing']) {
                    $newRemainingAmount += $subtractAmount;
                    // 如果剩余金额大于0，取消结束状态
                    if ($newRemainingAmount > 0) {
                        $isFinished = 0;
                    }
                }
            }

            // 所有月份都使用统一的月份状态管理
            // 管理月份状态
            if ($monthlyState) {
                $stmt = $pdo->prepare("
                    UPDATE record_monthly_states
                    SET is_completed = ?, completed_at = ?, updated_at = NOW()
                    WHERE record_id = ? AND view_month = ?
                ");
                $completedAt = $newCompleted ? date('Y-m-d H:i:s') : null;
                $stmt->execute([$newCompleted, $completedAt, $recordId, $viewMonth]);
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO record_monthly_states (record_id, view_month, is_completed, completed_at)
                    VALUES (?, ?, ?, ?)
                ");
                $completedAt = $newCompleted ? date('Y-m-d H:i:s') : null;
                $stmt->execute([$recordId, $viewMonth, $newCompleted, $completedAt]);
            }

            // 如果是当前月份，同时更新记录的缓存字段（为了兼容性）
            if ($viewMonth === $currentMonth) {
                // 重新计算当前月份的累计金额和剩余金额
                $currentAccumulated = calculateAccumulatedAmount($pdo, $recordId, $currentMonth);
                $currentRemaining = floatval($record['amount']) - $currentAccumulated;
                $currentFinished = ($record['is_decreasing'] && $currentRemaining <= 0) ? 1 : 0;

                if ($record['is_decreasing']) {
                    $stmt = $pdo->prepare("UPDATE records SET is_completed = ?, accumulated_amount = ?, remaining_amount = ?, is_finished = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$newCompleted, $currentAccumulated, $currentRemaining, $currentFinished, $recordId]);
                } else {
                    $stmt = $pdo->prepare("UPDATE records SET is_completed = ?, accumulated_amount = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$newCompleted, $currentAccumulated, $recordId]);
                }
            }

            // 获取最终的累计金额（所有月份都重新计算）
            $newAccumulatedAmount = calculateAccumulatedAmount($pdo, $recordId, $viewMonth);

            echo json_encode([
                'success' => true,
                'message' => '状态更新成功',
                'data' => [
                    'record_id' => $recordId,
                    'view_month' => $viewMonth,
                    'is_completed' => $newCompleted,
                    'accumulated_amount' => $newAccumulatedAmount
                ]
            ]);
            break;

        case 'fix_accumulated_amounts':
            // 修复累计金额的特殊操作
            $currentMonth = date('Y-m');

            // 获取所有记录
            $stmt = $pdo->prepare("SELECT * FROM records ORDER BY id");
            $stmt->execute();
            $records = $stmt->fetchAll();

            $fixedCount = 0;

            foreach ($records as $record) {
                // 重新计算当前月份的累计金额
                $correctAmount = calculateAccumulatedAmount($pdo, $record['id'], $currentMonth);

                // 更新记录
                $stmt = $pdo->prepare("UPDATE records SET accumulated_amount = ? WHERE id = ?");
                $stmt->execute([$correctAmount, $record['id']]);

                $fixedCount++;
            }

            echo json_encode([
                'success' => true,
                'message' => "已修复 {$fixedCount} 条记录的累计金额",
                'fixed_count' => $fixedCount
            ]);
            break;

        case 'clear_historical_states':
            // 清理历史月份状态记录的特殊操作
            $currentMonth = date('Y-m');

            // 删除所有历史月份的完成状态记录
            $stmt = $pdo->prepare("DELETE FROM record_monthly_states WHERE view_month < ?");
            $stmt->execute([$currentMonth]);
            $deletedCount = $stmt->rowCount();

            echo json_encode([
                'success' => true,
                'message' => "已清理 {$deletedCount} 条历史月份状态记录",
                'deleted_count' => $deletedCount
            ]);
            break;

        default:
            http_response_code(404);
            echo json_encode(['error' => '未知操作']);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => '服务器错误',
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}

// 计算累计金额的函数
function calculateAccumulatedAmount($pdo, $recordId, $viewMonth) {
    // 获取记录信息
    $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
    $stmt->execute([$recordId]);
    $record = $stmt->fetch();

    if (!$record) {
        return 0;
    }

    $totalAmount = 0;
    $currentMonth = date('Y-m');

    // 优化：直接查询所有相关的月份状态，避免逐月查询
    $stmt = $pdo->prepare("
        SELECT rms.view_month, rms.is_completed
        FROM record_monthly_states rms
        WHERE rms.record_id = ? AND rms.view_month <= ? AND rms.is_completed = 1
        ORDER BY rms.view_month
    ");
    $stmt->execute([$recordId, $viewMonth]);
    $monthlyStates = $stmt->fetchAll();

    // 将月份状态转换为关联数组，便于查找
    $monthlyStateMap = [];
    foreach ($monthlyStates as $state) {
        $monthlyStateMap[$state['view_month']] = true;
    }

    // 计算月份状态的累计金额
    foreach ($monthlyStates as $state) {
        $monthStr = $state['view_month'];
        $isRenewalMonth = isRenewalMonthForDate($record, $monthStr);
        $addAmount = $isRenewalMonth ? floatval($record['renewal_amount']) : floatval($record['monthly_amount']);
        $totalAmount += $addAmount;
    }

    // 检查当前月份的原始记录状态
    if ($record['is_completed'] && !isset($monthlyStateMap[$currentMonth])) {
        // 当前月份的记录已完成，但没有月份状态记录，说明是在当前月份完成的
        // 只有当查看月份 >= 当前月份时，才计算当前月份的金额
        if ($viewMonth >= $currentMonth) {
            $isRenewalMonth = isRenewalMonthForDate($record, $currentMonth);
            $addAmount = $isRenewalMonth ? floatval($record['renewal_amount']) : floatval($record['monthly_amount']);
            $totalAmount += $addAmount;
        }
    }

    return $totalAmount;
}

// 判断指定月份是否为续期月份
function isRenewalMonthForDate($record, $monthStr) {
    $recordDate = $record['date'];
    $renewalTime = $record['renewal_time'];

    if (empty($renewalTime) || $renewalTime === '永久') {
        return false;
    }

    $monthsToAdd = 0;
    switch ($renewalTime) {
        case '二个月':
            $monthsToAdd = 2;
            break;
        case '三个月':
            $monthsToAdd = 3;
            break;
        case '六个月':
            $monthsToAdd = 6;
            break;
        default:
            return false;
    }

    $startDate = new DateTime($recordDate);
    $viewDateTime = new DateTime($monthStr . '-01');

    // 检查记录创建月份是否就是续期月份
    if ($startDate->format('Y-m') === $monthStr) {
        return true;
    }

    // 向未来检查续期月份
    $futureDate = clone $startDate;
    for ($i = 1; $i <= 120; $i++) { // 检查未来10年
        $futureDate->add(new DateInterval("P{$monthsToAdd}M"));
        if ($futureDate->format('Y-m') === $monthStr) {
            return true;
        }
        // 如果已经超过查看月份太多，停止检查
        if ($futureDate->format('Y') - $viewDateTime->format('Y') > 10) {
            break;
        }
    }

    // 向历史检查续期月份
    $pastDate = clone $startDate;
    for ($i = 1; $i <= 120; $i++) { // 检查过去10年
        $pastDate->sub(new DateInterval("P{$monthsToAdd}M"));
        if ($pastDate->format('Y-m') === $monthStr) {
            return true;
        }
        // 如果已经回到太早的日期，停止检查
        if ($viewDateTime->format('Y') - $pastDate->format('Y') > 10) {
            break;
        }
    }

    return false;
}
?>
