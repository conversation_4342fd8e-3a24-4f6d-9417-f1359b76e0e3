-- 创建数据库表结构

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL UNIQUE,
    `email` varchar(100) NOT NULL UNIQUE,
    `password` varchar(255) NOT NULL,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 账本表
CREATE TABLE IF NOT EXISTS `account_books` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `name` varchar(100) NOT NULL,
    `description` text,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    <PERSON>IMAR<PERSON> KEY (`id`),
    <PERSON>OREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 记账记录表
CREATE TABLE IF NOT EXISTS `records` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `account_book_id` int(11) NOT NULL,
    `date` date NOT NULL,
    `name` varchar(100) NOT NULL,
    `amount` decimal(10,2) NOT NULL,
    `monthly_amount` decimal(10,2) NOT NULL,
    `renewal_time` varchar(50) NOT NULL,
    `renewal_amount` decimal(10,2) NOT NULL,
    `remark` text,
    `accumulated_amount` decimal(10,2) DEFAULT 0,
    `is_completed` tinyint(1) DEFAULT 0,
    `completed_month` varchar(7), -- 格式: YYYY-MM
    `is_locked` tinyint(1) DEFAULT 0, -- 是否被月度锁定
    `is_decreasing` tinyint(1) DEFAULT 0, -- 是否为递减形式
    `remaining_amount` decimal(10,2) DEFAULT 0, -- 剩余金额（递减形式使用）
    `is_finished` tinyint(1) DEFAULT 0, -- 是否已结束（递减形式清零后）
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`account_book_id`) REFERENCES `account_books`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入默认管理员用户
INSERT INTO `users` (`username`, `email`, `password`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');
-- 密码是: password
