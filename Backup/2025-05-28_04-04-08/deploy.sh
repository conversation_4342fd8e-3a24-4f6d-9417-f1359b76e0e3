#!/bin/bash

# 记账管理系统部署脚本

echo "开始部署记账管理系统..."

# 检查是否在正确的目录
if [ ! -f "README.md" ]; then
    echo "错误：请在项目根目录执行此脚本"
    exit 1
fi

# 1. 数据库初始化
echo "1. 初始化数据库..."
if command -v mysql &> /dev/null; then
    echo "请手动执行以下命令初始化数据库："
    echo "mysql -u shuju -p shuju < backend/database/init.sql"
else
    echo "未找到 mysql 命令，请在宝塔面板的 phpMyAdmin 中导入 backend/database/init.sql"
fi

# 2. 检查 PHP 版本
echo "2. 检查 PHP 环境..."
if command -v php &> /dev/null; then
    PHP_VERSION=$(php -v | head -n 1 | cut -d " " -f 2 | cut -d "." -f 1,2)
    echo "PHP 版本: $PHP_VERSION"
    if [ "$(echo "$PHP_VERSION >= 7.4" | bc)" -eq 1 ]; then
        echo "✓ PHP 版本满足要求"
    else
        echo "⚠ 警告：建议使用 PHP 7.4 或更高版本"
    fi
else
    echo "⚠ 警告：未找到 PHP 命令"
fi

# 3. 设置文件权限
echo "3. 设置文件权限..."
chmod -R 755 backend/
chmod -R 644 backend/api/*.php
chmod +x backend/cron/monthly_reset.php

# 4. 前端构建
echo "4. 构建前端..."
cd frontend

if command -v npm &> /dev/null; then
    echo "安装前端依赖..."
    npm install
    
    echo "构建生产版本..."
    npm run build
    
    if [ -d "dist" ]; then
        echo "✓ 前端构建成功"
    else
        echo "✗ 前端构建失败"
        exit 1
    fi
else
    echo "⚠ 警告：未找到 npm 命令，请手动构建前端"
    echo "在 frontend 目录执行："
    echo "npm install"
    echo "npm run build"
fi

cd ..

# 5. 检查配置文件
echo "5. 检查配置文件..."
if [ -f "backend/api/config.php" ]; then
    echo "✓ 后端配置文件存在"
else
    echo "✗ 后端配置文件不存在"
    exit 1
fi

if [ -f ".htaccess" ]; then
    echo "✓ .htaccess 文件存在"
else
    echo "✗ .htaccess 文件不存在"
    exit 1
fi

# 6. 创建日志目录
echo "6. 创建日志目录..."
mkdir -p logs
chmod 755 logs

# 7. 显示部署信息
echo ""
echo "=========================================="
echo "部署完成！"
echo "=========================================="
echo ""
echo "接下来的步骤："
echo "1. 确保数据库已正确初始化"
echo "2. 检查数据库连接配置（backend/api/config.php）"
echo "3. 确保 Web 服务器支持 .htaccess 重写"
echo "4. 访问网站进行测试"
echo ""
echo "可选配置："
echo "- 添加定时任务实现月度自动重置："
echo "  0 0 1 * * php $(pwd)/backend/cron/monthly_reset.php"
echo ""
echo "默认管理员账户："
echo "用户名: admin"
echo "密码: password"
echo ""
echo "项目目录结构："
echo "├── backend/          # 后端 PHP 代码"
echo "├── frontend/dist/    # 前端构建文件"
echo "├── .htaccess         # Apache 重写规则"
echo "└── README.md         # 详细文档"
echo ""
echo "如有问题，请查看 README.md 文档"
