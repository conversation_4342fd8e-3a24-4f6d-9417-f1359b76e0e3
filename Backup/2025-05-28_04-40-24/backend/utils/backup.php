<?php

require_once '../api/config.php';

/**
 * 数据库备份工具
 */
class DatabaseBackup {
    private $pdo;
    private $backupDir;
    
    public function __construct() {
        $this->pdo = (new Database())->getConnection();
        $this->backupDir = __DIR__ . '/../backups';
        
        // 创建备份目录
        if (!is_dir($this->backupDir)) {
            mkdir($this->backupDir, 0755, true);
        }
    }
    
    /**
     * 创建数据库备份
     */
    public function createBackup($filename = null) {
        if (!$filename) {
            $filename = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $filepath = $this->backupDir . '/' . $filename;
        $sql = $this->generateBackupSQL();
        
        if (file_put_contents($filepath, $sql)) {
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath,
                'size' => filesize($filepath)
            ];
        }
        
        return ['success' => false, 'error' => '备份文件写入失败'];
    }
    
    /**
     * 生成备份SQL
     */
    private function generateBackupSQL() {
        $sql = "-- 数据库备份\n";
        $sql .= "-- 生成时间: " . date('Y-m-d H:i:s') . "\n";
        $sql .= "-- 数据库: " . DB_NAME . "\n\n";
        
        $sql .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
        
        $tables = ['users', 'account_books', 'records'];
        
        foreach ($tables as $table) {
            $sql .= $this->getTableStructure($table);
            $sql .= $this->getTableData($table);
            $sql .= "\n";
        }
        
        $sql .= "SET FOREIGN_KEY_CHECKS=1;\n";
        
        return $sql;
    }
    
    /**
     * 获取表结构
     */
    private function getTableStructure($table) {
        $stmt = $this->pdo->query("SHOW CREATE TABLE `$table`");
        $row = $stmt->fetch();
        
        $sql = "-- 表结构: $table\n";
        $sql .= "DROP TABLE IF EXISTS `$table`;\n";
        $sql .= $row['Create Table'] . ";\n\n";
        
        return $sql;
    }
    
    /**
     * 获取表数据
     */
    private function getTableData($table) {
        $stmt = $this->pdo->query("SELECT * FROM `$table`");
        $rows = $stmt->fetchAll();
        
        if (empty($rows)) {
            return "-- 表 $table 无数据\n\n";
        }
        
        $sql = "-- 表数据: $table\n";
        
        foreach ($rows as $row) {
            $values = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $values[] = 'NULL';
                } else {
                    $values[] = "'" . addslashes($value) . "'";
                }
            }
            
            $columns = array_keys($row);
            $sql .= "INSERT INTO `$table` (`" . implode('`, `', $columns) . "`) VALUES (" . implode(', ', $values) . ");\n";
        }
        
        $sql .= "\n";
        return $sql;
    }
    
    /**
     * 获取备份列表
     */
    public function getBackupList() {
        $files = glob($this->backupDir . '/*.sql');
        $backups = [];
        
        foreach ($files as $file) {
            $backups[] = [
                'filename' => basename($file),
                'filepath' => $file,
                'size' => filesize($file),
                'created_at' => date('Y-m-d H:i:s', filemtime($file))
            ];
        }
        
        // 按创建时间倒序排列
        usort($backups, function($a, $b) {
            return filemtime($b['filepath']) - filemtime($a['filepath']);
        });
        
        return $backups;
    }
    
    /**
     * 删除备份文件
     */
    public function deleteBackup($filename) {
        $filepath = $this->backupDir . '/' . basename($filename);
        
        if (file_exists($filepath)) {
            return unlink($filepath);
        }
        
        return false;
    }
    
    /**
     * 恢复数据库
     */
    public function restoreBackup($filename) {
        $filepath = $this->backupDir . '/' . basename($filename);
        
        if (!file_exists($filepath)) {
            return ['success' => false, 'error' => '备份文件不存在'];
        }
        
        try {
            $sql = file_get_contents($filepath);
            $this->pdo->exec($sql);
            
            return ['success' => true, 'message' => '数据库恢复成功'];
        } catch (Exception $e) {
            return ['success' => false, 'error' => '恢复失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 自动清理旧备份
     */
    public function cleanOldBackups($keepDays = 30) {
        $files = glob($this->backupDir . '/*.sql');
        $cutoffTime = time() - ($keepDays * 24 * 60 * 60);
        $deletedCount = 0;
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoffTime) {
                if (unlink($file)) {
                    $deletedCount++;
                }
            }
        }
        
        return $deletedCount;
    }
}

// 命令行使用
if (php_sapi_name() === 'cli') {
    $backup = new DatabaseBackup();
    
    $action = $argv[1] ?? 'create';
    
    switch ($action) {
        case 'create':
            $result = $backup->createBackup();
            if ($result['success']) {
                echo "备份创建成功: {$result['filename']}\n";
                echo "文件大小: " . round($result['size'] / 1024, 2) . " KB\n";
            } else {
                echo "备份失败: {$result['error']}\n";
            }
            break;
            
        case 'list':
            $backups = $backup->getBackupList();
            echo "备份文件列表:\n";
            foreach ($backups as $file) {
                echo "- {$file['filename']} ({$file['created_at']}) - " . round($file['size'] / 1024, 2) . " KB\n";
            }
            break;
            
        case 'clean':
            $days = $argv[2] ?? 30;
            $count = $backup->cleanOldBackups($days);
            echo "清理了 $count 个超过 $days 天的备份文件\n";
            break;
            
        case 'restore':
            $filename = $argv[2] ?? '';
            if (empty($filename)) {
                echo "请指定要恢复的备份文件名\n";
                exit(1);
            }
            
            $result = $backup->restoreBackup($filename);
            if ($result['success']) {
                echo "数据库恢复成功\n";
            } else {
                echo "恢复失败: {$result['error']}\n";
            }
            break;
            
        default:
            echo "用法: php backup.php [create|list|clean|restore]\n";
            echo "  create          - 创建新备份\n";
            echo "  list            - 列出所有备份\n";
            echo "  clean [days]    - 清理旧备份（默认30天）\n";
            echo "  restore <file>  - 恢复指定备份\n";
    }
}
?>
