#!/bin/bash

# 自动备份脚本
# 用于定时备份网站和数据库

# 设置工作目录
cd "$(dirname "$0")"

# 日志文件
LOG_FILE="Backup/backup.log"

# 创建日志目录
mkdir -p Backup

# 记录开始时间
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始自动备份" >> "$LOG_FILE"

# 执行完整备份
php quick_backup.php full >> "$LOG_FILE" 2>&1

# 检查备份结果
if [ $? -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 备份成功完成" >> "$LOG_FILE"
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 备份失败" >> "$LOG_FILE"
fi

# 清理超过30天的日志
find Backup -name "backup.log" -mtime +30 -delete 2>/dev/null

echo "$(date '+%Y-%m-%d %H:%M:%S') - 自动备份脚本执行完成" >> "$LOG_FILE"
echo "----------------------------------------" >> "$LOG_FILE"
