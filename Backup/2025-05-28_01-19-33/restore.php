<?php
/**
 * 恢复脚本
 * 使用方法：php restore.php [备份目录名]
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

echo "=== 数据恢复工具 ===\n";
echo "开始时间: " . date('Y-m-d H:i:s') . "\n\n";

// 获取备份目录参数
$backupName = $argv[1] ?? null;
$backupDir = __DIR__ . '/Backup';

if (!$backupName) {
    echo "使用方法: php restore.php [备份目录名]\n\n";
    echo "可用的备份:\n";
    
    if (is_dir($backupDir)) {
        $backups = array_filter(scandir($backupDir), function($item) use ($backupDir) {
            return $item != '.' && $item != '..' && is_dir($backupDir . '/' . $item);
        });
        
        if (empty($backups)) {
            echo "  没有找到备份目录\n";
        } else {
            foreach ($backups as $backup) {
                $backupPath = $backupDir . '/' . $backup;
                $backupTime = date('Y-m-d H:i:s', filemtime($backupPath));
                echo "  - $backup (创建时间: $backupTime)\n";
            }
        }
    } else {
        echo "  备份目录不存在: $backupDir\n";
    }
    
    exit(1);
}

$restoreDir = $backupDir . '/' . $backupName;

if (!is_dir($restoreDir)) {
    echo "错误: 备份目录不存在: $restoreDir\n";
    exit(1);
}

echo "恢复目录: $restoreDir\n";

// 数据库配置
$dbConfig = [
    'host' => 'shuju-mysql.ns-qqzrupud.svc',
    'port' => '3306',
    'name' => 'shuju',
    'user' => 'root',
    'pass' => 'gb5bjqq9'
];

// 确认恢复操作
echo "\n⚠️  警告: 此操作将覆盖当前的数据库和文件！\n";
echo "是否继续? (输入 'yes' 确认): ";
$handle = fopen("php://stdin", "r");
$confirmation = trim(fgets($handle));
fclose($handle);

if (strtolower($confirmation) !== 'yes') {
    echo "恢复操作已取消\n";
    exit(0);
}

echo "\n开始恢复...\n";

// 1. 恢复数据库
echo "\n1. 恢复数据库...\n";

$sqlFiles = [
    $restoreDir . '/database.sql',
    $restoreDir . '/database_backup.sql'
];

$sqlFile = null;
foreach ($sqlFiles as $file) {
    if (file_exists($file)) {
        $sqlFile = $file;
        break;
    }
}

if ($sqlFile) {
    echo "   找到数据库备份文件: " . basename($sqlFile) . "\n";
    
    // 先清空数据库
    echo "   清空现有数据库...\n";
    $clearCommand = sprintf(
        'mysql -h%s -P%s -u%s -p%s -e "DROP DATABASE IF EXISTS %s; CREATE DATABASE %s CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"',
        escapeshellarg($dbConfig['host']),
        escapeshellarg($dbConfig['port']),
        escapeshellarg($dbConfig['user']),
        escapeshellarg($dbConfig['pass']),
        escapeshellarg($dbConfig['name']),
        escapeshellarg($dbConfig['name'])
    );
    
    exec($clearCommand, $output, $returnCode);
    
    if ($returnCode === 0) {
        echo "   ✓ 数据库清空成功\n";
        
        // 恢复数据库
        echo "   导入备份数据...\n";
        $restoreCommand = sprintf(
            'mysql -h%s -P%s -u%s -p%s %s < %s 2>&1',
            escapeshellarg($dbConfig['host']),
            escapeshellarg($dbConfig['port']),
            escapeshellarg($dbConfig['user']),
            escapeshellarg($dbConfig['pass']),
            escapeshellarg($dbConfig['name']),
            escapeshellarg($sqlFile)
        );
        
        exec($restoreCommand, $restoreOutput, $restoreCode);
        
        if ($restoreCode === 0) {
            echo "   ✓ 数据库恢复成功\n";
        } else {
            echo "   ✗ 数据库恢复失败\n";
            if (!empty($restoreOutput)) {
                echo "   错误: " . implode("\n", $restoreOutput) . "\n";
            }
        }
    } else {
        echo "   ✗ 数据库清空失败\n";
        if (!empty($output)) {
            echo "   错误: " . implode("\n", $output) . "\n";
        }
    }
} else {
    echo "   ⚠️  未找到数据库备份文件\n";
}

// 2. 恢复网站文件
echo "\n2. 恢复网站文件...\n";

$websiteBackupDir = $restoreDir . '/website';
if (is_dir($websiteBackupDir)) {
    echo "   找到网站备份目录\n";
    
    // 备份当前文件（可选）
    $currentBackupDir = $backupDir . '/current_backup_' . date('Y-m-d_H-i-s');
    mkdir($currentBackupDir, 0755, true);
    
    $importantFiles = ['index.html', 'api_direct.php', '.htaccess'];
    foreach ($importantFiles as $file) {
        if (file_exists(__DIR__ . '/' . $file)) {
            copy(__DIR__ . '/' . $file, $currentBackupDir . '/' . $file);
        }
    }
    echo "   ✓ 当前文件已备份到: " . basename($currentBackupDir) . "\n";
    
    // 恢复文件
    $restoredCount = 0;
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($websiteBackupDir, RecursiveDirectoryIterator::SKIP_DOTS)
    );
    
    foreach ($iterator as $file) {
        $relativePath = substr($file->getPathname(), strlen($websiteBackupDir) + 1);
        $destPath = __DIR__ . '/' . $relativePath;
        
        if ($file->isFile()) {
            $destDir = dirname($destPath);
            if (!is_dir($destDir)) {
                mkdir($destDir, 0755, true);
            }
            
            if (copy($file->getPathname(), $destPath)) {
                $restoredCount++;
            }
        }
    }
    
    echo "   ✓ 恢复了 $restoredCount 个文件\n";
} else {
    // 尝试直接从备份目录恢复文件
    echo "   从备份目录直接恢复文件...\n";
    
    $files = ['index.html', 'api_direct.php', '.htaccess'];
    $restoredCount = 0;
    
    foreach ($files as $file) {
        $sourcePath = $restoreDir . '/' . $file;
        $destPath = __DIR__ . '/' . $file;
        
        if (file_exists($sourcePath)) {
            if (copy($sourcePath, $destPath)) {
                echo "   ✓ 恢复文件: $file\n";
                $restoredCount++;
            } else {
                echo "   ✗ 恢复文件失败: $file\n";
            }
        }
    }
    
    echo "   恢复了 $restoredCount 个文件\n";
}

// 3. 设置文件权限
echo "\n3. 设置文件权限...\n";
chmod(__DIR__ . '/index.html', 0644);
chmod(__DIR__ . '/api_direct.php', 0644);
if (file_exists(__DIR__ . '/.htaccess')) {
    chmod(__DIR__ . '/.htaccess', 0644);
}
echo "   ✓ 文件权限设置完成\n";

// 4. 验证恢复结果
echo "\n4. 验证恢复结果...\n";

// 检查关键文件
$keyFiles = ['index.html', 'api_direct.php'];
$missingFiles = [];

foreach ($keyFiles as $file) {
    if (file_exists(__DIR__ . '/' . $file)) {
        echo "   ✓ $file 存在\n";
    } else {
        echo "   ✗ $file 缺失\n";
        $missingFiles[] = $file;
    }
}

// 检查数据库连接
try {
    $pdo = new PDO(
        "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['name']};charset=utf8mb4",
        $dbConfig['user'],
        $dbConfig['pass']
    );
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $userCount = $stmt->fetch()['count'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM records");
    $recordCount = $stmt->fetch()['count'];
    
    echo "   ✓ 数据库连接正常\n";
    echo "   - 用户数量: $userCount\n";
    echo "   - 记录数量: $recordCount\n";
    
} catch (Exception $e) {
    echo "   ✗ 数据库连接失败: " . $e->getMessage() . "\n";
}

echo "\n=== 恢复完成 ===\n";

if (empty($missingFiles)) {
    echo "✓ 所有关键文件恢复成功\n";
    echo "✓ 网站应该可以正常访问了\n";
} else {
    echo "⚠️  以下文件恢复失败:\n";
    foreach ($missingFiles as $file) {
        echo "  - $file\n";
    }
}

echo "\n建议:\n";
echo "1. 检查网站是否可以正常访问\n";
echo "2. 测试登录功能\n";
echo "3. 验证数据完整性\n";
echo "4. 如有问题，可以从备份目录手动恢复文件\n";
?>
