/*M!999999\- enable the sandbox mode */ 
-- Maria<PERSON><PERSON> dump 10.19  Distrib 10.11.11-MariaDB, for debian-linux-gnu (x86_64)
--
-- Host: shuju-mysql.ns-qqzrupud.svc    Database: shuju
-- ------------------------------------------------------
-- Server version	8.0.30

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `account_books`
--

DROP TABLE IF EXISTS `account_books`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `account_books` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_user_created` (`user_id`,`created_at`),
  CONSTRAINT `account_books_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `account_books`
--

LOCK TABLES `account_books` WRITE;
/*!40000 ALTER TABLE `account_books` DISABLE KEYS */;
INSERT INTO `account_books` VALUES
(1,1,'默认账本','系统自动创建的默认账本','2025-05-24 18:38:05','2025-05-24 18:38:05'),
(2,1,'个人账本','','2025-05-24 20:42:23','2025-05-24 20:42:23'),
(6,1,'烂账','','2025-05-25 09:25:29','2025-05-25 09:25:29'),
(7,7,'默认账本','系统自动创建的默认账本','2025-05-25 20:05:45','2025-05-25 20:05:45'),
(9,8,'默认账本','系统自动创建的默认账本','2025-05-27 21:21:09','2025-05-27 21:21:09');
/*!40000 ALTER TABLE `account_books` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `record_monthly_states`
--

DROP TABLE IF EXISTS `record_monthly_states`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `record_monthly_states` (
  `id` int NOT NULL AUTO_INCREMENT,
  `record_id` int NOT NULL,
  `view_month` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '格式: 2024-05',
  `is_completed` tinyint(1) DEFAULT '0',
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_record_month` (`record_id`,`view_month`),
  KEY `idx_record_month` (`record_id`,`view_month`),
  KEY `idx_month_completed` (`view_month`,`is_completed`),
  CONSTRAINT `record_monthly_states_ibfk_1` FOREIGN KEY (`record_id`) REFERENCES `records` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=126 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='记录月份状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `record_monthly_states`
--

LOCK TABLES `record_monthly_states` WRITE;
/*!40000 ALTER TABLE `record_monthly_states` DISABLE KEYS */;
INSERT INTO `record_monthly_states` VALUES
(38,12,'2025-04',1,'2025-05-26 21:28:58','2025-05-26 21:09:20','2025-05-26 21:28:58'),
(39,3,'2025-04',1,'2025-05-26 21:28:48','2025-05-26 21:19:12','2025-05-26 21:28:48'),
(40,3,'2025-06',0,NULL,'2025-05-26 21:19:17','2025-05-27 20:47:48'),
(41,3,'2025-07',0,NULL,'2025-05-26 21:19:21','2025-05-26 21:19:23'),
(44,2,'2025-01',0,NULL,'2025-05-26 21:25:53','2025-05-26 21:25:54'),
(45,1,'2025-01',1,'2025-05-26 21:25:55','2025-05-26 21:25:55','2025-05-26 21:25:55'),
(46,3,'2025-01',1,'2025-05-26 21:25:56','2025-05-26 21:25:56','2025-05-26 21:25:56'),
(47,65,'2025-01',1,'2025-05-26 21:26:00','2025-05-26 21:26:00','2025-05-26 21:26:00'),
(48,6,'2025-01',1,'2025-05-26 21:26:05','2025-05-26 21:26:05','2025-05-26 21:26:05'),
(49,9,'2025-01',0,NULL,'2025-05-26 21:26:08','2025-05-26 21:26:40'),
(50,14,'2025-01',1,'2025-05-26 21:26:51','2025-05-26 21:26:36','2025-05-26 21:26:51'),
(51,15,'2025-01',1,'2025-05-26 21:26:51','2025-05-26 21:26:37','2025-05-26 21:26:51'),
(52,17,'2025-01',0,NULL,'2025-05-26 21:27:09','2025-05-26 21:27:11'),
(53,2,'2025-02',0,NULL,'2025-05-26 21:27:14','2025-05-26 21:27:15'),
(54,1,'2025-02',1,'2025-05-26 21:27:16','2025-05-26 21:27:16','2025-05-26 21:27:16'),
(55,3,'2025-02',1,'2025-05-26 21:27:16','2025-05-26 21:27:16','2025-05-26 21:27:16'),
(56,65,'2025-02',1,'2025-05-26 21:27:19','2025-05-26 21:27:19','2025-05-26 21:27:19'),
(57,6,'2025-02',1,'2025-05-26 21:27:20','2025-05-26 21:27:20','2025-05-26 21:27:20'),
(58,14,'2025-02',1,'2025-05-26 21:27:34','2025-05-26 21:27:34','2025-05-26 21:27:34'),
(59,15,'2025-02',1,'2025-05-26 21:27:36','2025-05-26 21:27:36','2025-05-26 21:27:36'),
(60,16,'2025-02',1,'2025-05-26 21:27:37','2025-05-26 21:27:37','2025-05-26 21:27:37'),
(61,17,'2025-02',0,NULL,'2025-05-26 21:27:39','2025-05-26 21:27:44'),
(62,2,'2025-03',1,'2025-05-26 21:27:56','2025-05-26 21:27:56','2025-05-26 21:27:56'),
(63,1,'2025-03',1,'2025-05-26 21:27:57','2025-05-26 21:27:57','2025-05-26 21:27:57'),
(64,3,'2025-03',1,'2025-05-26 21:27:59','2025-05-26 21:27:59','2025-05-26 21:27:59'),
(65,65,'2025-03',1,'2025-05-26 21:28:01','2025-05-26 21:28:01','2025-05-26 21:28:01'),
(66,6,'2025-03',1,'2025-05-26 21:28:03','2025-05-26 21:28:03','2025-05-26 21:28:03'),
(67,12,'2025-03',1,'2025-05-26 21:28:17','2025-05-26 21:28:17','2025-05-26 21:28:17'),
(68,14,'2025-03',1,'2025-05-26 21:28:34','2025-05-26 21:28:34','2025-05-26 21:28:34'),
(69,15,'2025-03',1,'2025-05-26 21:28:35','2025-05-26 21:28:35','2025-05-26 21:28:35'),
(70,16,'2025-03',1,'2025-05-26 21:28:37','2025-05-26 21:28:37','2025-05-26 21:28:37'),
(71,17,'2025-03',1,'2025-05-26 21:28:38','2025-05-26 21:28:38','2025-05-26 21:28:38'),
(72,2,'2025-04',1,'2025-05-26 21:28:47','2025-05-26 21:28:47','2025-05-26 21:28:47'),
(73,1,'2025-04',1,'2025-05-26 21:28:48','2025-05-26 21:28:48','2025-05-26 21:28:48'),
(74,65,'2025-04',1,'2025-05-26 21:28:50','2025-05-26 21:28:50','2025-05-26 21:28:50'),
(75,6,'2025-04',1,'2025-05-26 21:28:52','2025-05-26 21:28:52','2025-05-26 21:28:52'),
(76,9,'2025-04',1,'2025-05-26 21:28:56','2025-05-26 21:28:56','2025-05-26 21:28:56'),
(77,14,'2025-04',1,'2025-05-26 21:29:02','2025-05-26 21:29:02','2025-05-26 21:29:02'),
(78,15,'2025-04',1,'2025-05-26 21:29:02','2025-05-26 21:29:02','2025-05-26 21:29:02'),
(79,16,'2025-04',1,'2025-05-26 21:29:03','2025-05-26 21:29:03','2025-05-26 21:29:03'),
(80,17,'2025-04',1,'2025-05-26 21:29:04','2025-05-26 21:29:04','2025-05-26 21:29:04'),
(81,8,'2025-01',1,'2025-05-26 21:32:03','2025-05-26 21:32:03','2025-05-26 21:32:03'),
(82,11,'2025-01',1,'2025-05-26 21:32:06','2025-05-26 21:32:06','2025-05-26 21:32:06'),
(83,58,'2025-01',1,'2025-05-26 21:32:09','2025-05-26 21:32:09','2025-05-26 21:32:09'),
(84,8,'2025-02',1,'2025-05-26 21:32:14','2025-05-26 21:32:14','2025-05-26 21:32:14'),
(85,11,'2025-02',1,'2025-05-26 21:32:17','2025-05-26 21:32:17','2025-05-26 21:32:17'),
(86,58,'2025-02',1,'2025-05-26 21:32:19','2025-05-26 21:32:19','2025-05-26 21:32:19'),
(87,8,'2025-03',1,'2025-05-26 21:32:22','2025-05-26 21:32:22','2025-05-26 21:32:22'),
(88,11,'2025-03',1,'2025-05-26 21:32:26','2025-05-26 21:32:26','2025-05-26 21:32:26'),
(89,58,'2025-03',1,'2025-05-26 21:32:45','2025-05-26 21:32:30','2025-05-26 21:32:45'),
(90,8,'2025-04',1,'2025-05-26 21:33:42','2025-05-26 21:33:42','2025-05-26 21:33:42'),
(91,11,'2025-04',1,'2025-05-26 21:33:48','2025-05-26 21:33:48','2025-05-26 21:33:48'),
(92,58,'2025-04',1,'2025-05-26 21:33:49','2025-05-26 21:33:49','2025-05-26 21:33:49'),
(114,10,'2025-04',0,NULL,'2025-05-27 19:45:16','2025-05-27 21:30:51'),
(115,10,'2025-06',0,NULL,'2025-05-27 20:02:31','2025-05-27 20:24:10'),
(116,57,'2025-04',0,NULL,'2025-05-27 20:24:14','2025-05-27 20:24:15'),
(117,2,'2025-06',0,NULL,'2025-05-27 20:47:41','2025-05-27 20:47:43'),
(118,1,'2025-06',0,NULL,'2025-05-27 20:47:45','2025-05-27 20:47:46'),
(119,19,'2025-01',0,NULL,'2025-05-27 21:35:43','2025-05-27 21:36:25'),
(120,20,'2025-01',0,NULL,'2025-05-27 21:35:50','2025-05-27 21:35:52'),
(121,22,'2025-01',0,NULL,'2025-05-27 21:35:53','2025-05-27 21:36:26'),
(122,21,'2025-01',0,NULL,'2025-05-27 21:35:55','2025-05-27 21:36:27'),
(123,23,'2025-01',0,NULL,'2025-05-27 21:36:01','2025-05-27 21:36:27'),
(124,24,'2025-01',0,NULL,'2025-05-27 21:36:04','2025-05-27 21:36:28'),
(125,25,'2025-01',0,NULL,'2025-05-27 21:36:06','2025-05-27 21:36:28');
/*!40000 ALTER TABLE `record_monthly_states` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `records`
--

DROP TABLE IF EXISTS `records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `records` (
  `id` int NOT NULL AUTO_INCREMENT,
  `account_book_id` int NOT NULL,
  `date` date NOT NULL,
  `name` varchar(100) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `monthly_amount` decimal(10,2) NOT NULL,
  `renewal_time` varchar(50) NOT NULL,
  `renewal_amount` decimal(10,2) NOT NULL,
  `remark` text,
  `accumulated_amount` decimal(10,2) DEFAULT '0.00',
  `is_completed` tinyint(1) DEFAULT '0',
  `completed_month` varchar(7) DEFAULT NULL,
  `is_locked` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_decreasing` tinyint(1) DEFAULT '0' COMMENT 'æ˜¯å¦ä¸ºé€’å‡å½¢å¼',
  `remaining_amount` decimal(10,2) DEFAULT '0.00' COMMENT 'å‰©ä½™é‡‘é¢ï¼ˆé€’å‡å½¢å¼ä½¿ç”¨ï¼‰',
  `is_finished` tinyint(1) DEFAULT '0' COMMENT 'æ˜¯å¦å·²ç»“æŸï¼ˆé€’å‡å½¢å¼æ¸…é›¶åŽï¼‰',
  PRIMARY KEY (`id`),
  KEY `account_book_id` (`account_book_id`),
  KEY `idx_account_book_date` (`account_book_id`,`date`),
  KEY `idx_account_book_created` (`account_book_id`,`created_at`),
  KEY `idx_is_decreasing` (`is_decreasing`),
  KEY `idx_is_completed` (`is_completed`),
  CONSTRAINT `records_ibfk_1` FOREIGN KEY (`account_book_id`) REFERENCES `account_books` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `records`
--

LOCK TABLES `records` WRITE;
/*!40000 ALTER TABLE `records` DISABLE KEYS */;
INSERT INTO `records` VALUES
(1,1,'2025-03-05','金碧',30000.00,3000.00,'三个月',6000.00,'',18000.00,1,NULL,0,'2025-05-24 18:52:32','2025-05-27 18:08:14',0,0.00,0),
(2,1,'2025-05-02','诗雅',20000.00,1600.00,'二个月',3200.00,'',8000.00,1,'2025-05',0,'2025-05-24 18:53:15','2025-05-27 18:08:10',0,0.00,0),
(3,1,'2025-04-06','诗雅',30000.00,2400.00,'三个月',4800.00,'',16800.00,1,'2025-05',0,'2025-05-24 22:08:40','2025-05-27 18:08:17',0,0.00,0),
(5,1,'2025-05-15','阿健',20000.00,2000.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-24 22:16:28','2025-05-27 17:39:23',0,20000.00,0),
(6,1,'2025-03-15','阿俊',60000.00,3000.00,'三个月',4800.00,'',16800.00,1,NULL,0,'2025-05-25 05:59:59','2025-05-28 06:39:43',0,60000.00,0),
(8,1,'2025-05-08','波哥',20000.00,2000.00,'永久',0.00,'',10000.00,1,NULL,0,'2025-05-25 08:51:13','2025-05-27 18:08:19',0,0.00,0),
(9,1,'2025-05-18','阿俊',5000.00,600.00,'永久',0.00,'',1200.00,1,NULL,0,'2025-05-25 08:53:33','2025-05-27 17:21:46',0,5000.00,0),
(10,1,'2025-05-15','波姐',85000.00,5000.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 08:53:57','2025-05-28 06:37:54',1,85000.00,0),
(11,1,'2025-04-18','波哥',20000.00,2000.00,'三个月',4000.00,'',14000.00,1,NULL,0,'2025-05-25 08:54:40','2025-05-27 17:21:46',0,20000.00,0),
(12,1,'2025-05-21','顺哥',15000.00,900.00,'三个月',1800.00,'',1800.00,0,NULL,0,'2025-05-25 08:55:26','2025-05-27 17:21:46',0,15000.00,0),
(13,1,'2025-05-25','阿桦',30000.00,2000.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 08:56:15','2025-05-27 17:21:46',0,30000.00,0),
(14,1,'2025-04-26','阿俊',15000.00,1500.00,'三个月',2300.00,'',9100.00,1,'2025-05',0,'2025-05-25 08:56:41','2025-05-27 17:21:46',0,15000.00,0),
(15,1,'2025-03-27','阿俊',25000.00,1250.00,'三个月',2000.00,'',7000.00,1,NULL,0,'2025-05-25 08:57:28','2025-05-27 18:08:04',0,0.00,0),
(16,1,'2025-05-29','阿俊',15000.00,1200.00,'三个月',2000.00,'',4400.00,0,NULL,0,'2025-05-25 08:58:40','2025-05-27 17:21:46',0,15000.00,0),
(17,1,'2025-05-30','辉记',82000.00,4100.00,'永久',0.00,'',8200.00,0,NULL,0,'2025-05-25 08:59:10','2025-05-27 17:21:46',0,82000.00,0),
(18,1,'2025-05-31','何生',80000.00,1600.00,'永久',0.00,'递减，10月开始',0.00,0,NULL,0,'2025-05-25 08:59:43','2025-05-27 17:39:58',1,80000.00,0),
(19,2,'2025-05-01','健儿',10000.00,1000.00,'三个月',2000.00,'',2000.00,1,NULL,0,'2025-05-25 09:02:01','2025-05-27 20:48:32',0,0.00,0),
(20,2,'2025-05-03','荷花',50000.00,2000.00,'永久',0.00,'',2000.00,1,NULL,0,'2025-05-25 09:05:48','2025-05-27 17:21:46',0,50000.00,0),
(21,2,'2025-05-04','老豆',150000.00,6000.00,'永久',0.00,'递减',6000.00,1,NULL,0,'2025-05-25 09:06:27','2025-05-27 17:21:46',0,150000.00,0),
(22,2,'2025-05-04','伟健',100000.00,2000.00,'永久',0.00,'',2000.00,1,NULL,0,'2025-05-25 09:06:48','2025-05-27 17:21:46',0,100000.00,0),
(23,2,'2025-05-05','荷花',300000.00,10000.00,'永久',0.00,'',10000.00,1,NULL,0,'2025-05-25 09:07:16','2025-05-27 17:21:46',0,300000.00,0),
(24,2,'2025-05-08','郭永俊',40000.00,800.00,'永久',0.00,'',800.00,1,NULL,0,'2025-05-25 09:07:39','2025-05-27 17:21:46',0,40000.00,0),
(25,2,'2025-03-10','飞哥',70000.00,3000.00,'三个月',4500.00,'',3000.00,1,NULL,0,'2025-05-25 09:08:16','2025-05-27 17:21:46',0,70000.00,0),
(26,2,'2025-03-11','荷花',20000.00,1500.00,'三个月',3000.00,'',1500.00,1,NULL,0,'2025-05-25 09:09:20','2025-05-27 17:21:46',0,20000.00,0),
(27,2,'2025-05-13','荷花',20000.00,800.00,'永久',0.00,'',800.00,1,NULL,0,'2025-05-25 09:09:46','2025-05-27 17:21:46',0,20000.00,0),
(28,2,'2025-05-13','荷花',10000.00,800.00,'二个月',1600.00,'',1600.00,1,NULL,0,'2025-05-25 09:10:16','2025-05-27 17:21:46',0,10000.00,0),
(29,2,'2025-05-15','淦明',250000.00,7500.00,'永久',0.00,'',7500.00,1,NULL,0,'2025-05-25 09:10:41','2025-05-27 17:21:46',0,250000.00,0),
(30,2,'2025-05-16','老豆',23000.00,400.00,'永久',0.00,'信用卡',400.00,1,NULL,0,'2025-05-25 09:11:07','2025-05-27 17:21:46',0,23000.00,0),
(31,2,'2025-05-17','荷花',50000.00,2000.00,'永久',0.00,'',2000.00,1,NULL,0,'2025-05-25 09:11:30','2025-05-27 17:21:46',0,50000.00,0),
(32,2,'2025-05-20','荷花',30000.00,1500.00,'三个月',3000.00,'',3000.00,1,NULL,0,'2025-05-25 09:12:04','2025-05-27 17:21:46',0,30000.00,0),
(33,2,'2025-05-20','荷花',130000.00,6800.00,'永久',0.00,'',6800.00,1,NULL,0,'2025-05-25 09:12:23','2025-05-27 17:21:46',0,130000.00,0),
(34,2,'2025-05-25','老乡',20000.00,2000.00,'永久',0.00,'递减',2000.00,1,NULL,0,'2025-05-25 09:12:50','2025-05-27 17:21:46',0,20000.00,0),
(35,2,'2025-03-26','老婆',30000.00,1500.00,'三个月',2400.00,'',0.00,0,NULL,0,'2025-05-25 09:13:34','2025-05-27 20:48:40',0,30000.00,0),
(36,2,'2025-03-27','荷花',25000.00,1250.00,'三个月',2000.00,'阿俊',1250.00,1,NULL,0,'2025-05-25 09:14:16','2025-05-27 18:02:15',0,25000.00,0),
(37,2,'2025-05-29','荷花',50000.00,2000.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:14:38','2025-05-27 17:21:46',0,50000.00,0),
(38,2,'2025-05-30','淦明',100000.00,3000.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:15:03','2025-05-27 17:21:46',0,100000.00,0),
(39,2,'2025-05-31','外父',300000.00,10500.00,'永久',0.00,'递减',0.00,0,NULL,0,'2025-05-25 09:16:48','2025-05-27 17:21:46',0,300000.00,0),
(40,2,'2025-05-31','郭永俊',200000.00,4220.00,'永久',0.00,'递减',0.00,0,NULL,0,'2025-05-25 09:17:16','2025-05-27 17:21:46',0,200000.00,0),
(41,2,'2025-05-31','郭永俊',100000.00,1500.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:18:00','2025-05-27 17:21:46',0,100000.00,0),
(42,2,'2025-05-31','郭永俊',60000.00,1250.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:18:17','2025-05-27 17:21:46',0,60000.00,0),
(43,2,'2025-05-31','外母',300000.00,5000.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:18:36','2025-05-27 17:21:46',0,300000.00,0),
(44,2,'2025-05-31','老婆',15000.00,1200.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:19:02','2025-05-27 17:21:46',0,15000.00,0),
(45,2,'2025-05-31','飞哥',200000.00,6000.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:19:20','2025-05-27 17:21:46',0,200000.00,0),
(46,2,'2025-05-15','飞哥',200000.00,6000.00,'永久',0.00,'',6000.00,1,NULL,0,'2025-05-25 09:19:42','2025-05-27 17:21:46',0,200000.00,0),
(47,2,'2025-05-15','飞哥',150000.00,6600.00,'永久',0.00,'',6600.00,1,NULL,0,'2025-05-25 09:23:47','2025-05-27 17:21:46',0,150000.00,0),
(48,2,'2025-05-19','老豆',300000.00,9000.00,'永久',0.00,'递减',9000.00,1,NULL,0,'2025-05-25 09:24:27','2025-05-27 17:21:46',0,300000.00,0),
(49,6,'2025-05-01','关颖琪',6000.00,0.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:25:53','2025-05-27 17:21:46',0,6000.00,0),
(50,6,'2025-05-01','莫嘉荣',50000.00,0.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:26:19','2025-05-27 17:21:46',0,50000.00,0),
(51,6,'2025-05-01','古文龙',10000.00,0.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:26:36','2025-05-27 17:21:46',0,10000.00,0),
(52,6,'2025-05-01','黄星云',7000.00,0.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:27:07','2025-05-27 17:21:46',0,7000.00,0),
(53,6,'2025-05-01','苗国亮',6000.00,0.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:28:11','2025-05-27 17:21:46',0,6000.00,0),
(54,6,'2025-05-25','梁宇桦',30000.00,2000.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:29:24','2025-05-27 17:21:46',0,30000.00,0),
(55,6,'2025-05-15','阿健',20000.00,0.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 09:29:50','2025-05-27 17:21:46',0,20000.00,0),
(56,6,'2025-05-01','庞总',186500.00,0.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 11:27:12','2025-05-27 17:21:46',0,186500.00,0),
(57,1,'2025-05-10','波哥',100000.00,3000.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 11:27:42','2025-05-28 03:54:17',0,100000.00,0),
(58,1,'2025-05-21','波哥',15000.00,1500.00,'永久',0.00,'',7500.00,1,NULL,0,'2025-05-25 11:28:02','2025-05-27 17:21:46',0,15000.00,0),
(59,7,'2025-03-10','飞哥',70000.00,3000.00,'三个月',4500.00,'',0.00,0,NULL,0,'2025-05-25 20:06:50','2025-05-27 17:21:46',0,70000.00,0),
(60,7,'2025-05-01','荷花女',10000.00,1000.00,'三个月',2000.00,'',0.00,0,NULL,0,'2025-05-25 20:09:30','2025-05-27 17:21:46',0,10000.00,0),
(61,7,'2025-05-15','机佬',25600.00,1400.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 20:10:51','2025-05-27 17:21:46',0,25600.00,0),
(62,7,'2025-05-30','荷花男',15000.00,1200.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 20:12:00','2025-05-27 17:21:46',0,15000.00,0),
(63,7,'2025-03-27','阿俊',30000.00,1500.00,'三个月',2400.00,'',0.00,0,NULL,0,'2025-05-25 20:12:42','2025-05-27 17:21:46',0,30000.00,0),
(64,7,'2025-05-26','何',10000.00,500.00,'永久',0.00,'',0.00,0,NULL,0,'2025-05-25 20:13:15','2025-05-27 17:21:46',0,10000.00,0),
(65,1,'2025-04-14','石头',10000.00,300.00,'三个月',600.00,'',2100.00,1,NULL,0,'2025-05-26 20:40:18','2025-05-27 17:21:46',0,10000.00,0),
(67,2,'2025-05-26','荷花',20000.00,1000.00,'三个月',2000.00,'',2000.00,1,NULL,0,'2025-05-26 21:30:23','2025-05-27 17:21:46',0,20000.00,0);
/*!40000 ALTER TABLE `records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_email` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES
(1,'admin','<EMAIL>','$2y$10$P/mAfgeeSVhXw721PYHXdu/HePjZwj2LT1ayU7ntyMwmuQA.k1xIO','2025-05-24 18:20:14','2025-05-24 19:15:58'),
(7,'Maggie','<EMAIL>','$2y$10$RosZKjeLmsk3jLcC44m.T.61IF3KsEtq3T9ekZf/F4NmlFF7hYQq2','2025-05-25 20:05:45','2025-05-25 20:05:45'),
(8,'Seven','<EMAIL>','$2y$10$rPw/9oJ1EdBtyTKXxE.mb.UDEMHX97Q5RnW0wFq7N4RVMdwCjzmWy','2025-05-27 21:21:09','2025-05-27 21:21:09');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-28  6:46:10
