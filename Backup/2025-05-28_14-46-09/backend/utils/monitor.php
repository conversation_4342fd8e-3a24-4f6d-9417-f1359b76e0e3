<?php

require_once '../api/config.php';

/**
 * 系统监控工具
 */
class SystemMonitor {
    private $pdo;
    private $logFile;
    
    public function __construct() {
        try {
            $this->pdo = (new Database())->getConnection();
        } catch (Exception $e) {
            $this->pdo = null;
        }
        
        $this->logFile = __DIR__ . '/../logs/monitor.log';
        
        // 创建日志目录
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * 检查系统状态
     */
    public function checkSystemHealth() {
        $status = [
            'timestamp' => date('Y-m-d H:i:s'),
            'overall' => 'healthy',
            'checks' => []
        ];
        
        // 数据库连接检查
        $status['checks']['database'] = $this->checkDatabase();
        
        // 磁盘空间检查
        $status['checks']['disk_space'] = $this->checkDiskSpace();
        
        // 文件权限检查
        $status['checks']['file_permissions'] = $this->checkFilePermissions();
        
        // 内存使用检查
        $status['checks']['memory'] = $this->checkMemoryUsage();
        
        // 错误日志检查
        $status['checks']['error_logs'] = $this->checkErrorLogs();
        
        // 确定整体状态
        foreach ($status['checks'] as $check) {
            if ($check['status'] === 'error') {
                $status['overall'] = 'error';
                break;
            } elseif ($check['status'] === 'warning' && $status['overall'] === 'healthy') {
                $status['overall'] = 'warning';
            }
        }
        
        return $status;
    }
    
    /**
     * 检查数据库连接
     */
    private function checkDatabase() {
        if (!$this->pdo) {
            return [
                'status' => 'error',
                'message' => '数据库连接失败',
                'details' => null
            ];
        }
        
        try {
            // 检查表是否存在
            $tables = ['users', 'account_books', 'records'];
            $missingTables = [];
            
            foreach ($tables as $table) {
                $stmt = $this->pdo->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() === 0) {
                    $missingTables[] = $table;
                }
            }
            
            if (!empty($missingTables)) {
                return [
                    'status' => 'error',
                    'message' => '缺少数据表',
                    'details' => ['missing_tables' => $missingTables]
                ];
            }
            
            // 检查数据统计
            $stats = [];
            foreach ($tables as $table) {
                $stmt = $this->pdo->query("SELECT COUNT(*) as count FROM $table");
                $stats[$table] = $stmt->fetch()['count'];
            }
            
            return [
                'status' => 'healthy',
                'message' => '数据库连接正常',
                'details' => ['table_counts' => $stats]
            ];
            
        } catch (Exception $e) {
            return [
                'status' => 'error',
                'message' => '数据库查询失败',
                'details' => ['error' => $e->getMessage()]
            ];
        }
    }
    
    /**
     * 检查磁盘空间
     */
    private function checkDiskSpace() {
        $freeBytes = disk_free_space('.');
        $totalBytes = disk_total_space('.');
        $usedBytes = $totalBytes - $freeBytes;
        $usagePercent = round(($usedBytes / $totalBytes) * 100, 2);
        
        $status = 'healthy';
        $message = '磁盘空间充足';
        
        if ($usagePercent > 90) {
            $status = 'error';
            $message = '磁盘空间不足';
        } elseif ($usagePercent > 80) {
            $status = 'warning';
            $message = '磁盘空间紧张';
        }
        
        return [
            'status' => $status,
            'message' => $message,
            'details' => [
                'usage_percent' => $usagePercent,
                'free_space' => $this->formatBytes($freeBytes),
                'total_space' => $this->formatBytes($totalBytes)
            ]
        ];
    }
    
    /**
     * 检查文件权限
     */
    private function checkFilePermissions() {
        $criticalPaths = [
            '../api/config.php' => 'readable',
            '../logs' => 'writable',
            '../backups' => 'writable'
        ];
        
        $issues = [];
        
        foreach ($criticalPaths as $path => $requirement) {
            $fullPath = __DIR__ . '/' . $path;
            
            if (!file_exists($fullPath)) {
                $issues[] = "$path 不存在";
                continue;
            }
            
            if ($requirement === 'readable' && !is_readable($fullPath)) {
                $issues[] = "$path 不可读";
            }
            
            if ($requirement === 'writable' && !is_writable($fullPath)) {
                $issues[] = "$path 不可写";
            }
        }
        
        if (empty($issues)) {
            return [
                'status' => 'healthy',
                'message' => '文件权限正常',
                'details' => null
            ];
        }
        
        return [
            'status' => 'error',
            'message' => '文件权限问题',
            'details' => ['issues' => $issues]
        ];
    }
    
    /**
     * 检查内存使用
     */
    private function checkMemoryUsage() {
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $usagePercent = round(($memoryUsage / $memoryLimit) * 100, 2);
        
        $status = 'healthy';
        $message = '内存使用正常';
        
        if ($usagePercent > 90) {
            $status = 'warning';
            $message = '内存使用过高';
        }
        
        return [
            'status' => $status,
            'message' => $message,
            'details' => [
                'usage_percent' => $usagePercent,
                'current_usage' => $this->formatBytes($memoryUsage),
                'memory_limit' => $this->formatBytes($memoryLimit)
            ]
        ];
    }
    
    /**
     * 检查错误日志
     */
    private function checkErrorLogs() {
        $errorLogPath = ini_get('error_log');
        
        if (!$errorLogPath || !file_exists($errorLogPath)) {
            return [
                'status' => 'healthy',
                'message' => '无错误日志文件',
                'details' => null
            ];
        }
        
        // 检查最近24小时的错误
        $recentErrors = $this->getRecentErrors($errorLogPath, 24);
        
        if (count($recentErrors) > 10) {
            return [
                'status' => 'warning',
                'message' => '最近24小时错误较多',
                'details' => ['error_count' => count($recentErrors)]
            ];
        }
        
        return [
            'status' => 'healthy',
            'message' => '错误日志正常',
            'details' => ['error_count' => count($recentErrors)]
        ];
    }
    
    /**
     * 获取最近的错误日志
     */
    private function getRecentErrors($logPath, $hours) {
        if (!file_exists($logPath)) {
            return [];
        }
        
        $cutoffTime = time() - ($hours * 3600);
        $errors = [];
        
        $handle = fopen($logPath, 'r');
        if ($handle) {
            while (($line = fgets($handle)) !== false) {
                if (preg_match('/^\[(\d{2}-\w{3}-\d{4} \d{2}:\d{2}:\d{2})/', $line, $matches)) {
                    $logTime = strtotime($matches[1]);
                    if ($logTime > $cutoffTime) {
                        $errors[] = trim($line);
                    }
                }
            }
            fclose($handle);
        }
        
        return $errors;
    }
    
    /**
     * 记录监控日志
     */
    public function logStatus($status) {
        $logEntry = [
            'timestamp' => $status['timestamp'],
            'overall' => $status['overall'],
            'summary' => []
        ];
        
        foreach ($status['checks'] as $check => $result) {
            $logEntry['summary'][$check] = $result['status'];
        }
        
        $logLine = json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($this->logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * 解析内存限制
     */
    private function parseMemoryLimit($limit) {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit) - 1]);
        $limit = (int) $limit;
        
        switch ($last) {
            case 'g':
                $limit *= 1024;
            case 'm':
                $limit *= 1024;
            case 'k':
                $limit *= 1024;
        }
        
        return $limit;
    }
}

// 命令行使用
if (php_sapi_name() === 'cli') {
    $monitor = new SystemMonitor();
    $status = $monitor->checkSystemHealth();
    
    echo "系统监控报告 - " . $status['timestamp'] . "\n";
    echo "整体状态: " . strtoupper($status['overall']) . "\n\n";
    
    foreach ($status['checks'] as $check => $result) {
        $icon = $result['status'] === 'healthy' ? '✓' : ($result['status'] === 'warning' ? '⚠' : '✗');
        echo "$icon $check: {$result['message']}\n";
        
        if ($result['details']) {
            foreach ($result['details'] as $key => $value) {
                if (is_array($value)) {
                    echo "  $key: " . implode(', ', $value) . "\n";
                } else {
                    echo "  $key: $value\n";
                }
            }
        }
        echo "\n";
    }
    
    // 记录日志
    $monitor->logStatus($status);
    
    // 如果有错误，退出码为1
    if ($status['overall'] === 'error') {
        exit(1);
    }
}
?>
