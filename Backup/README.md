# 🛡️ 备份系统使用说明

## 📋 概述

这是一个完整的网站和数据库备份系统，包含自动备份、手动备份、恢复和管理功能。

## 🗂️ 文件结构

```
项目根目录/
├── backup.php              # 完整备份脚本
├── quick_backup.php         # 快速备份脚本
├── restore.php             # 恢复脚本
├── auto_backup.sh          # 自动备份Shell脚本
├── backup_manager.html     # 备份管理Web界面
├── backup_api.php          # 备份管理API
└── Backup/                 # 备份存储目录
    ├── README.md           # 本说明文件
    ├── backup.log          # 备份日志
    └── [备份目录]/         # 各个备份的存储目录
```

## 🚀 使用方法

### 1. 手动备份

#### 完整备份（推荐，默认）
```bash
php quick_backup.php
# 或明确指定
php quick_backup.php full
```
- 备份数据库和所有重要文件（17个文件）
- 执行速度适中（10-20秒）
- 适合日常备份和重要节点

#### 快速备份（精简版）
```bash
php quick_backup.php quick
```
- 只备份核心文件（5个文件）
- 执行速度快（5-10秒）
- 适合频繁备份

#### 高级完整备份
```bash
php backup.php
```
- 备份所有文件和数据库
- 创建压缩包
- 自动清理旧备份

### 2. 自动备份

#### 设置定时任务
```bash
# 编辑crontab
crontab -e

# 添加以下行（每天凌晨2点自动备份）
0 2 * * * /path/to/your/project/auto_backup.sh

# 或者每6小时备份一次
0 */6 * * * /path/to/your/project/auto_backup.sh
```

### 3. 恢复数据

#### 查看可用备份
```bash
php restore.php
```

#### 恢复指定备份
```bash
php restore.php 2025-05-27_16-43-47
```

### 4. Web管理界面

访问 `backup_manager.html` 可以通过Web界面管理备份：
- 创建新备份
- 查看备份列表
- 下载备份文件
- 删除旧备份

## 📦 备份内容

### 数据库备份
- 完整的MySQL数据库导出
- 包含所有表结构和数据
- 文件名：`database.sql` 或 `database_backup.sql`

### 文件备份
- `index.html` - 主页面
- `api_direct.php` - API接口
- `backend/` - 后端目录
- `.htaccess` - 重写规则
- `README.md` - 说明文档

## 🔧 配置说明

### 数据库配置
在备份脚本中修改以下配置：
```php
$dbConfig = [
    'host' => 'shuju-mysql.ns-qqzrupud.svc',
    'port' => '3306',
    'name' => 'shuju',
    'user' => 'root',
    'pass' => 'gb5bjqq9'
];
```

### 备份保留策略
- 默认保留最新的5个备份
- 自动清理超过30天的日志
- 可在脚本中修改保留数量

## 📊 备份信息

每个备份都包含以下信息：
- 备份时间
- 备份类型（quick/full）
- 数据库文件
- 复制的文件数量
- 恢复说明

## ⚠️ 注意事项

### 安全性
1. 备份文件包含敏感数据，请妥善保管
2. 定期测试恢复流程
3. 建议将备份文件存储到安全的位置

### 权限要求
1. PHP需要有读写权限
2. MySQL需要有导出权限
3. Shell脚本需要执行权限

### 磁盘空间
1. 确保有足够的磁盘空间存储备份
2. 定期清理旧备份
3. 监控备份目录大小

## 🔄 恢复流程

### 完整恢复步骤
1. 选择要恢复的备份
2. 确认恢复操作（会覆盖现有数据）
3. 自动备份当前文件
4. 恢复数据库
5. 恢复文件
6. 设置权限
7. 验证恢复结果

### 验证恢复
恢复完成后，请检查：
- [ ] 网站可以正常访问
- [ ] 登录功能正常
- [ ] 数据完整性
- [ ] 文件权限正确

## 📞 故障排除

### 常见问题

#### 1. 数据库备份失败
- 检查数据库连接配置
- 确认MySQL权限
- 查看错误日志

#### 2. 文件备份失败
- 检查文件权限
- 确认磁盘空间
- 查看PHP错误日志

#### 3. 恢复失败
- 确认备份文件完整性
- 检查目标目录权限
- 验证数据库连接

### 日志查看
```bash
# 查看备份日志
tail -f Backup/backup.log

# 查看PHP错误日志
tail -f /var/log/php_errors.log
```

## 📈 最佳实践

1. **定期备份**：建议每天至少备份一次
2. **测试恢复**：定期测试恢复流程
3. **多地备份**：将备份文件复制到多个位置
4. **监控空间**：定期检查备份目录大小
5. **文档更新**：保持备份说明文档最新

## 🆘 紧急恢复

如果网站完全无法访问：

1. 通过SSH连接服务器
2. 进入项目目录
3. 运行恢复脚本：
   ```bash
   php restore.php [备份目录名]
   ```
4. 按提示确认恢复操作
5. 等待恢复完成
6. 测试网站功能

---

**备份是数据安全的最后一道防线，请务必重视！** 🛡️
