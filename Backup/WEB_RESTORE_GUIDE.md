# 🌐 Web备份管理界面使用指南

## 📋 概述

`backup_manager.html` 是一个功能完整的Web备份管理界面，支持：
- ✅ **创建备份**
- ✅ **查看备份列表**
- ✅ **下载备份文件**
- ✅ **手动恢复数据**（新增功能）
- ✅ **删除旧备份**

## 🚀 访问方式

在浏览器中访问：
```
http://你的域名/backup_manager.html
```

## 🔄 恢复功能详解

### **恢复选项**

#### **1. 🔄 完整恢复（推荐）**
- **恢复内容**：数据库 + 所有网站文件
- **适用场景**：网站完全损坏、需要回滚到历史版本
- **恢复时间**：30-60秒
- **安全性**：最高（完全恢复到备份时的状态）

#### **2. 🗄️ 仅恢复数据库**
- **恢复内容**：只恢复数据库，保留当前网站文件
- **适用场景**：数据丢失但网站代码正常
- **恢复时间**：10-20秒
- **安全性**：中等（可能存在代码与数据不匹配）

#### **3. 📁 仅恢复文件**
- **恢复内容**：只恢复网站文件，保留当前数据库
- **适用场景**：代码损坏但数据正常
- **恢复时间**：20-40秒
- **安全性**：中等（可能存在代码与数据不匹配）

### **恢复流程**

#### **步骤1：选择备份**
1. 在备份列表中找到要恢复的备份
2. 查看备份时间和大小确认
3. 点击 **🔄 恢复** 按钮

#### **步骤2：确认恢复**
1. 系统显示恢复确认对话框
2. 选择恢复类型（完整/数据库/文件）
3. 阅读警告信息
4. 点击 **✅ 确认恢复**

#### **步骤3：自动执行**
1. 系统自动创建当前状态备份
2. 执行恢复操作
3. 显示恢复结果
4. 完整恢复后提示刷新页面

### **安全机制**

#### **🛡️ 恢复前自动备份**
- 每次恢复前自动创建当前状态备份
- 备份名称：`before_restore_YYYY-MM-DD_HH-MM-SS`
- 包含恢复信息文件：`restore_info.json`
- 如果恢复失败可以回滚

#### **📋 恢复记录**
每次恢复都会记录：
```json
{
    "restore_time": "2025-05-27 17:16:06",
    "backup_name": "2025-05-28_01-10-49",
    "restore_type": "database",
    "current_backup": "before_restore_2025-05-27_17-16-06",
    "results": [
        "✓ 当前数据库已备份",
        "✓ 数据库恢复成功"
    ]
}
```

#### **🔒 安全检查**
- 路径安全验证
- 备份文件完整性检查
- 数据库连接验证
- 权限检查

## ⚠️ 重要注意事项

### **恢复前准备**
1. **确认备份时间点**：确保选择正确的备份
2. **通知用户**：如果是生产环境，提前通知用户
3. **检查磁盘空间**：确保有足够空间存储恢复前备份
4. **备份当前状态**：系统会自动备份，但建议手动再备份一次

### **恢复后验证**
1. **功能测试**：测试网站主要功能
2. **数据检查**：验证数据完整性
3. **用户验证**：确认用户可以正常登录
4. **性能检查**：确认网站性能正常

### **常见问题处理**

#### **恢复失败**
- 检查备份文件完整性
- 查看错误日志：`before_restore_*/restore_error.log`
- 验证数据库连接
- 检查文件权限

#### **恢复后问题**
- 使用恢复前备份回滚
- 检查代码与数据版本匹配
- 清理浏览器缓存
- 重启Web服务

## 🎯 最佳实践

### **恢复策略**
1. **测试环境先试**：重要恢复先在测试环境验证
2. **分步恢复**：先恢复数据库，测试后再恢复文件
3. **保留多个备份**：不要只依赖一个备份
4. **定期验证**：定期测试恢复流程

### **备份管理**
1. **定期清理**：删除过期的恢复前备份
2. **监控空间**：关注备份目录磁盘使用
3. **文档记录**：记录重要的恢复操作
4. **权限控制**：限制恢复功能的访问权限

## 🔧 技术细节

### **支持的恢复类型**
- `full`：完整恢复（数据库+文件）
- `database`：仅数据库恢复
- `files`：仅文件恢复

### **API接口**
```javascript
// 恢复请求
POST /backup_api.php
{
    "action": "restore_backup",
    "backup_name": "2025-05-28_01-10-49",
    "restore_type": "full"
}

// 响应
{
    "success": true,
    "message": "恢复操作完成",
    "results": ["✓ 当前数据库已备份", "✓ 数据库恢复成功"],
    "current_backup": "before_restore_2025-05-27_17-16-06"
}
```

### **文件结构**
```
Backup/
├── 2025-05-28_01-10-49/          # 原始备份
│   ├── database.sql              # 数据库备份
│   ├── index.html               # 网站文件
│   └── ...
├── before_restore_2025-05-27_17-16-06/  # 恢复前备份
│   ├── current_database.sql     # 恢复前数据库
│   ├── restore_info.json        # 恢复信息
│   └── ...
└── README.md                    # 说明文档
```

## 🆘 紧急恢复

如果Web界面无法访问，可以使用命令行恢复：

```bash
# 查看可用备份
php restore.php

# 恢复指定备份
php restore.php 2025-05-28_01-10-49
```

---

**现在你可以通过Web界面安全地管理和恢复备份了！** 🎉
