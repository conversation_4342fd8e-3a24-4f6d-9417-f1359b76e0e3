-- 宝塔LNMP环境兼容的简化数据库脚本
-- 解决 utf8mb4_0900_ai_ci 排序规则不兼容问题

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 删除现有表（如果存在）
DROP TABLE IF EXISTS `record_monthly_states`;
DROP TABLE IF EXISTS `records`;
DROP TABLE IF EXISTS `account_books`;
DROP TABLE IF EXISTS `users`;

-- 创建用户表
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_email` (`email`),
  KEY `idx_users_email` (`email`),
  KEY `idx_users_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建账本表
CREATE TABLE `account_books` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `idx_user_created` (`user_id`,`created_at`),
  KEY `idx_books_user` (`user_id`),
  KEY `idx_books_created_at` (`created_at`),
  CONSTRAINT `account_books_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建记录表
CREATE TABLE `records` (
  `id` int NOT NULL AUTO_INCREMENT,
  `account_book_id` int NOT NULL,
  `date` date NOT NULL,
  `name` varchar(100) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `monthly_amount` decimal(10,2) NOT NULL,
  `renewal_time` varchar(50) NOT NULL,
  `renewal_amount` decimal(10,2) NOT NULL,
  `remark` text,
  `accumulated_amount` decimal(10,2) DEFAULT '0.00',
  `is_completed` tinyint(1) DEFAULT '0',
  `completed_month` varchar(7) DEFAULT NULL,
  `is_locked` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_decreasing` tinyint(1) DEFAULT '0' COMMENT '是否为递减形式',
  `remaining_amount` decimal(10,2) DEFAULT '0.00' COMMENT '剩余金额（递减形式使用）',
  `is_finished` tinyint(1) DEFAULT '0' COMMENT '是否已结束（递减形式清零后）',
  PRIMARY KEY (`id`),
  KEY `account_book_id` (`account_book_id`),
  KEY `idx_account_book_date` (`account_book_id`,`date`),
  KEY `idx_account_book_created` (`account_book_id`,`created_at`),
  KEY `idx_is_decreasing` (`is_decreasing`),
  KEY `idx_is_completed` (`is_completed`),
  KEY `idx_records_book_date` (`account_book_id`,`date`),
  KEY `idx_records_book_completed` (`account_book_id`,`is_completed`),
  KEY `idx_records_book_decreasing` (`account_book_id`,`is_decreasing`),
  KEY `idx_records_created_at` (`created_at`),
  CONSTRAINT `records_ibfk_1` FOREIGN KEY (`account_book_id`) REFERENCES `account_books` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建月份状态表
CREATE TABLE `record_monthly_states` (
  `id` int NOT NULL AUTO_INCREMENT,
  `record_id` int NOT NULL,
  `view_month` varchar(7) NOT NULL COMMENT '格式: 2024-05',
  `is_completed` tinyint(1) DEFAULT '0',
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_record_month` (`record_id`,`view_month`),
  KEY `idx_record_month` (`record_id`,`view_month`),
  KEY `idx_month_completed` (`view_month`,`is_completed`),
  KEY `idx_monthly_states_record_month` (`record_id`,`view_month`),
  KEY `idx_monthly_states_month` (`view_month`),
  CONSTRAINT `record_monthly_states_ibfk_1` FOREIGN KEY (`record_id`) REFERENCES `records` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='记录月份状态表';

-- 插入基础数据
-- 管理员用户（密码：password）
INSERT INTO `users` VALUES (1,'admin','<EMAIL>','$2y$10$P/mAfgeeSVhXw721PYHXdu/HePjZwj2LT1ayU7ntyMwmuQA.k1xIO','2025-05-24 18:20:14','2025-05-24 19:15:58');

-- 默认账本
INSERT INTO `account_books` VALUES (1,1,'默认账本','系统自动创建的默认账本','2025-05-24 18:38:05','2025-05-24 18:38:05');

-- 示例记录
INSERT INTO `records` VALUES 
(1,1,'2025-03-05','金碧',30000.00,3000.00,'三个月',6000.00,'',18000.00,1,NULL,0,'2025-05-24 18:52:32','2025-05-27 18:08:14',0,0.00,0),
(2,1,'2025-05-02','诗雅',20000.00,1600.00,'二个月',3200.00,'',8000.00,1,'2025-05',0,'2025-05-24 18:53:15','2025-05-27 18:08:10',0,0.00,0),
(3,1,'2025-04-06','诗雅',30000.00,2400.00,'三个月',4800.00,'',16800.00,1,'2025-05',0,'2025-05-24 22:08:40','2025-05-27 18:08:17',0,0.00,0);

-- 重置AUTO_INCREMENT
ALTER TABLE `users` AUTO_INCREMENT = 2;
ALTER TABLE `account_books` AUTO_INCREMENT = 2;
ALTER TABLE `records` AUTO_INCREMENT = 4;
ALTER TABLE `record_monthly_states` AUTO_INCREMENT = 1;

SET FOREIGN_KEY_CHECKS = 1;

-- 验证数据
SELECT '=== 数据库导入完成 ===' as status;
SELECT COUNT(*) as user_count FROM users;
SELECT COUNT(*) as book_count FROM account_books;
SELECT COUNT(*) as record_count FROM records;
SELECT '管理员账号: admin, 密码: password' as login_info;
