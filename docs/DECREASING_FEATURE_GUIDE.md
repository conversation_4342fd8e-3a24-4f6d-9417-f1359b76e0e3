# 📉 递减记账功能使用指南

## 🎯 功能概述

递减记账是一种新的记账方式，适用于需要逐月递减金额直至清零的场景，如分期付款、债务偿还、定期投资等。

### **核心特点**
- ✅ **自动递减**：每月完成标记后自动递减剩余金额
- ✅ **智能结束**：剩余金额为0时自动结束记账
- ✅ **保护机制**：已结束记录不允许再次完成标记
- ✅ **清晰显示**：界面显示"每月递减"而非"续期"信息

## 📋 使用方法

### **1. 创建递减记账**

#### **步骤1：填写基本信息**
- **记录名称**：如"金碧分期付款"
- **金额**：总金额，如 ¥1000
- **每月金额**：每月递减金额，如 ¥100

#### **步骤2：勾选递减形式**
- 在"每月金额"标题右侧勾选 **"递减形式"** 复选框
- 勾选后会显示提示信息：💡 递减形式：每月完成后会自动递减每月金额，直至金额为0时结束此记账

#### **步骤3：完成其他设置**
- **续期时间**：建议选择"永久"
- **续期金额**：建议设为0
- **备注**：可添加说明，如"1000-100x10=0"

### **2. 递减记账的显示**

#### **主界面显示**
普通记账显示：
```
月:¥300.00  7月续:¥600.00
```

递减记账显示：
```
月:¥300.00  每月递减:¥600.00
```

#### **详细信息显示**
- **剩余金额**：显示当前剩余未递减的金额
- **累计金额**：显示已累计的总金额
- **状态标识**：已结束的记录显示"已结束"标签

### **3. 完成标记过程**

#### **正常递减过程**
以金额1000、每月递减100为例：

| 完成次数 | 累计金额 | 剩余金额 | 状态 |
|---------|---------|---------|------|
| 第1次   | ¥100    | ¥900    | 进行中 |
| 第2次   | ¥200    | ¥800    | 进行中 |
| ...     | ...     | ...     | ... |
| 第10次  | ¥1000   | ¥0      | 已结束 |

#### **自动结束机制**
- 当剩余金额 ≤ 0 时，记录自动标记为"已结束"
- 已结束的记录不允许再次完成标记
- 系统提示："此记账已清零，请编辑或删除此记账"

## 🎨 界面特性

### **视觉标识**
- **递减标识**：显示"每月递减:¥xxx"
- **剩余金额**：显示当前剩余金额
- **已结束标签**：红色"已结束"标签
- **禁用状态**：已结束记录呈灰色显示

### **交互保护**
- **复选框禁用**：已结束记录的完成复选框被禁用
- **警告提示**：尝试操作已结束记录时显示警告
- **编辑提醒**：提示用户编辑或删除已结束记录

## 💡 使用场景

### **1. 分期付款**
```
记录名称：iPhone分期付款
总金额：¥6000
每月递减：¥500
预期完成：12个月
```

### **2. 债务偿还**
```
记录名称：信用卡还款
总金额：¥5000
每月递减：¥1000
预期完成：5个月
```

### **3. 定期投资**
```
记录名称：基金定投
总金额：¥12000
每月递减：¥1000
预期完成：12个月
```

### **4. 储蓄目标**
```
记录名称：旅游基金
总金额：¥3000
每月递减：¥300
预期完成：10个月
```

## 🔧 技术实现

### **数据库字段**
- `is_decreasing`：是否为递减形式（0/1）
- `remaining_amount`：剩余金额
- `is_finished`：是否已结束（0/1）

### **计算逻辑**
```php
// 完成标记时
if ($record['is_decreasing']) {
    $newRemainingAmount = $remainingAmount - $monthlyAmount;
    if ($newRemainingAmount <= 0) {
        $isFinished = 1; // 标记为结束
    }
}
```

### **保护机制**
```php
// 检查已结束记录
if ($record['is_decreasing'] && $record['is_finished']) {
    throw new Exception('此记账已清零，请编辑或删除此记账');
}
```

## ⚠️ 注意事项

### **使用建议**
1. **合理设置金额**：确保总金额能被每月金额整除
2. **定期检查**：关注剩余金额变化
3. **及时处理**：已结束记录及时编辑或删除
4. **备注说明**：添加计算说明便于理解

### **常见问题**
1. **Q：能否修改已创建的递减记录？**
   A：可以通过编辑功能修改，包括切换递减模式

2. **Q：已结束的记录如何处理？**
   A：可以编辑修改或直接删除

3. **Q：递减金额能否为小数？**
   A：支持小数，如每月递减¥99.99

4. **Q：能否取消递减模式？**
   A：可以通过编辑记录取消勾选递减形式

## 🎉 功能优势

### **相比普通记账**
- ✅ **目标明确**：有明确的结束时间点
- ✅ **进度可视**：剩余金额直观显示进度
- ✅ **自动管理**：无需手动计算剩余金额
- ✅ **防止错误**：已结束记录自动保护

### **适用性强**
- 💰 **财务管理**：分期、还款、储蓄
- 📊 **项目管理**：阶段性目标追踪
- 🎯 **个人目标**：习惯养成、技能提升
- 📈 **投资理财**：定投、定存管理

---

**递减记账功能让您的财务管理更加智能和高效！** 🚀✨
