# 📊 数据记账系统

一个简洁高效的个人记账管理系统，支持多账本管理、递减记账、月份查看等功能。

## 🚀 快速启动

```bash
# 一键启动服务器（自动处理端口占用）
./start.sh
```

访问地址：http://localhost:8080

## ✨ 主要功能

- **多账本管理**：支持创建和管理多个账本
- **记账管理**：添加、编辑、删除记账记录
- **递减记账**：支持每月递减的特殊记账类型
- **月份查看**：按月份查看和管理记录
- **统计分析**：实时统计和数据分析
- **数据备份**：支持数据备份和恢复

## 📁 项目结构

```
├── index.html          # 主页面
├── api_direct.php      # API接口
├── start.sh           # 服务器启动脚本
├── backup.php         # 数据备份
├── docs/              # 项目文档
│   ├── README.md      # 详细说明
│   ├── API_DOCUMENTATION.md  # API文档
│   └── DECREASING_FEATURE_GUIDE.md  # 递减功能说明
└── Backup/            # 备份文件夹
```

## 🛠️ 技术栈

- **前端**：HTML5, CSS3, JavaScript (原生)
- **后端**：PHP 7.4+
- **数据库**：MySQL 8.0
- **服务器**：PHP内置服务器

## 📖 使用说明

1. **启动系统**：运行 `./start.sh`
2. **注册登录**：首次使用需要注册账号
3. **创建账本**：登录后创建第一个账本
4. **添加记录**：开始记录收支数据
5. **查看统计**：实时查看数据统计

## 🔧 常见问题

### 服务器掉线怎么办？
直接重新运行 `./start.sh` 即可，脚本会自动处理端口占用问题。

### 如何备份数据？
访问 `/backup_manager.html` 进行数据备份和恢复。

### 递减记账是什么？
递减记账是一种特殊的记账类型，每次标记完成时会自动减少固定金额，适用于分期付款、债务还款等场景。

## 📚 更多文档

详细文档请查看 `docs/` 文件夹：
- [详细使用说明](docs/README.md)
- [API接口文档](docs/API_DOCUMENTATION.md)
- [递减功能说明](docs/DECREASING_FEATURE_GUIDE.md)

## 🎯 开发信息

- **版本**：v2.0
- **开发环境**：Devbox
- **数据库**：MySQL (shuju-mysql.ns-qqzrupud.svc:3306)
- **默认端口**：8080

---

💡 **提示**：如果遇到任何问题，请检查 `docs/` 文件夹中的相关文档。
