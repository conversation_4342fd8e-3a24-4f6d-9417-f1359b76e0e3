-- 数据库性能优化 - 添加索引
-- 这些索引不会改变表结构，只会提升查询性能

-- 分析当前索引状态
-- SHOW INDEX FROM records;
-- SHOW INDEX FROM account_books;
-- SHOW INDEX FROM record_monthly_states;
-- SHOW INDEX FROM users;

-- 为records表添加复合索引
-- 账本ID + 日期索引 - 优化按账本和日期查询
CREATE INDEX IF NOT EXISTS idx_records_book_date ON records(account_book_id, date);

-- 账本ID + 完成状态索引 - 优化按完成状态筛选
CREATE INDEX IF NOT EXISTS idx_records_book_completed ON records(account_book_id, is_completed);

-- 账本ID + 递减状态索引 - 优化递减记账查询
CREATE INDEX IF NOT EXISTS idx_records_book_decreasing ON records(account_book_id, is_decreasing);

-- 创建时间索引 - 优化按创建时间排序
CREATE INDEX IF NOT EXISTS idx_records_created_at ON records(created_at);

-- 为record_monthly_states表添加索引
-- 记录ID + 查看月份索引 - 优化月度状态查询
CREATE INDEX IF NOT EXISTS idx_monthly_states_record_month ON record_monthly_states(record_id, view_month);

-- 查看月份索引 - 优化按月份查询
CREATE INDEX IF NOT EXISTS idx_monthly_states_month ON record_monthly_states(view_month);

-- 为account_books表添加索引
-- 用户ID索引 - 优化用户账本查询
CREATE INDEX IF NOT EXISTS idx_books_user ON account_books(user_id);

-- 创建时间索引 - 优化按创建时间排序
CREATE INDEX IF NOT EXISTS idx_books_created_at ON account_books(created_at);

-- 为users表添加索引（如果需要）
-- 邮箱索引 - 优化登录查询
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- 用户名索引 - 优化登录查询
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);

-- 分析查询性能（执行后查看）
-- EXPLAIN SELECT * FROM records WHERE account_book_id = 1 ORDER BY date DESC;
-- EXPLAIN SELECT * FROM records WHERE account_book_id = 1 AND is_completed = 1;
-- EXPLAIN SELECT r.*, rms.is_completed as monthly_completed 
--         FROM records r 
--         LEFT JOIN record_monthly_states rms ON r.id = rms.record_id AND rms.view_month = '2024-05'
--         WHERE r.account_book_id = 1 
--         ORDER BY r.date DESC;

-- 优化建议：
-- 1. 定期运行 ANALYZE TABLE 来更新表统计信息
-- 2. 监控慢查询日志
-- 3. 根据实际使用情况调整索引

-- 执行统计信息更新
ANALYZE TABLE records;
ANALYZE TABLE account_books;
ANALYZE TABLE record_monthly_states;
ANALYZE TABLE users;
