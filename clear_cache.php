<?php
/**
 * 清除缓存工具
 * 帮助清除各种可能的缓存问题
 */

// 禁用缓存
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: text/html; charset=utf-8');

$results = [];

// 清除OPcache
if (function_exists('opcache_reset')) {
    if (opcache_reset()) {
        $results[] = '✅ OPcache已清除';
    } else {
        $results[] = '❌ OPcache清除失败';
    }
} else {
    $results[] = '⚠️ OPcache不可用';
}

// 清除文件状态缓存
if (function_exists('clearstatcache')) {
    clearstatcache();
    $results[] = '✅ 文件状态缓存已清除';
}

// 尝试清除可能的临时文件
$temp_dirs = [
    sys_get_temp_dir(),
    __DIR__ . '/temp',
    __DIR__ . '/cache',
    __DIR__ . '/tmp'
];

foreach ($temp_dirs as $dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '/*');
        $count = 0;
        foreach ($files as $file) {
            if (is_file($file) && is_writable($file)) {
                unlink($file);
                $count++;
            }
        }
        if ($count > 0) {
            $results[] = "✅ 清除了 $dir 中的 $count 个临时文件";
        }
    }
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧹 缓存清除工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #333; text-align: center; }
        .result {
            background: #e9ecef;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover { background: #0056b3; }
        .instructions {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 缓存清除工具</h1>
        
        <h2>📋 清除结果</h2>
        <?php foreach ($results as $result): ?>
            <div class="result <?php 
                if (strpos($result, '✅') !== false) echo 'success';
                elseif (strpos($result, '⚠️') !== false) echo 'warning';
                elseif (strpos($result, '❌') !== false) echo 'error';
            ?>">
                <?php echo htmlspecialchars($result); ?>
            </div>
        <?php endforeach; ?>

        <div class="instructions">
            <h3>🔄 接下来的步骤</h3>
            <ol>
                <li><strong>重启PHP服务</strong>：在宝塔面板重启PHP-FPM</li>
                <li><strong>重启Nginx</strong>：在宝塔面板重启Nginx服务</li>
                <li><strong>清除浏览器缓存</strong>：按Ctrl+F5强制刷新</li>
                <li><strong>检查版本</strong>：使用版本检查工具验证</li>
            </ol>
        </div>

        <h2>🛠️ 宝塔面板操作</h2>
        <div class="instructions">
            <h4>在宝塔面板中执行以下操作：</h4>
            <ol>
                <li>进入 <strong>软件商店</strong> → <strong>已安装</strong></li>
                <li>找到 <strong>PHP</strong> → 点击 <strong>设置</strong></li>
                <li>点击 <strong>重载配置</strong> 或 <strong>重启</strong></li>
                <li>找到 <strong>Nginx</strong> → 点击 <strong>重启</strong></li>
                <li>如果有 <strong>Redis</strong> 或 <strong>Memcached</strong>，也重启一下</li>
            </ol>
        </div>

        <h2>🌐 浏览器缓存清除</h2>
        <div class="instructions">
            <h4>不同浏览器的清除方法：</h4>
            <ul>
                <li><strong>Chrome/Edge</strong>：Ctrl+Shift+Delete → 选择"缓存的图片和文件"</li>
                <li><strong>Firefox</strong>：Ctrl+Shift+Delete → 选择"缓存"</li>
                <li><strong>Safari</strong>：Cmd+Option+E → 清空缓存</li>
                <li><strong>强制刷新</strong>：Ctrl+F5 (Windows) 或 Cmd+Shift+R (Mac)</li>
            </ul>
        </div>

        <h2>🔍 验证工具</h2>
        <a href="version_check.php" class="btn">📊 版本检查工具</a>
        <a href="index.html" class="btn">🏠 访问主页</a>
        <a href="?" class="btn">🔄 重新清除缓存</a>

        <div class="instructions">
            <h4>💡 如果问题仍然存在：</h4>
            <ul>
                <li>检查是否使用了CDN服务（如CloudFlare）</li>
                <li>确认域名DNS指向正确的服务器</li>
                <li>检查宝塔面板是否安装了缓存插件</li>
                <li>确认所有文件都已正确上传（包括.htaccess）</li>
                <li>尝试使用不同的浏览器或设备访问</li>
            </ul>
        </div>

        <div class="result warning">
            <strong>⚠️ 重要提醒</strong><br>
            清除缓存后，请等待1-2分钟再测试，让服务器完全重新加载所有文件。
        </div>
    </div>

    <script>
        // 自动刷新提示
        setTimeout(function() {
            if (confirm('缓存已清除，是否立即检查版本？')) {
                window.location.href = 'version_check.php';
            }
        }, 3000);
    </script>
</body>
</html>
