<?php
/**
 * 版本检查和缓存诊断工具
 * 帮助诊断为什么网站显示的不是最新版本
 */

// 设置时区
date_default_timezone_set('Asia/Shanghai');

// 禁用所有缓存
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');
header('Content-Type: text/html; charset=utf-8');

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 版本检查和缓存诊断工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2 { color: #333; }
        .info-box {
            background: #e9ecef;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th { background: #f8f9fa; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 版本检查和缓存诊断工具</h1>
        <p class="timestamp">检查时间: <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <div class="info-box warning">
            <strong>⚠️ 如果你看到这个页面，说明PHP正在正常工作</strong><br>
            如果这个页面显示的信息是旧的，那就是缓存问题。
        </div>

        <h2>📁 文件版本信息</h2>
        <?php
        $files_to_check = [
            'index.html' => '主页面',
            'api_direct.php' => 'API接口',
            'backup_manager.html' => '备份管理器',
            'download_backup.php' => '下载脚本',
            'database_simple.sql' => '简化数据库',
            'database_import_tool.html' => '导入工具',
            'version_check.php' => '当前检查脚本'
        ];

        echo '<table>';
        echo '<tr><th>文件名</th><th>描述</th><th>存在</th><th>大小</th><th>修改时间</th></tr>';
        
        foreach ($files_to_check as $file => $desc) {
            $exists = file_exists($file);
            $size = $exists ? filesize($file) : 0;
            $mtime = $exists ? date('Y-m-d H:i:s', filemtime($file)) : '不存在';
            
            echo '<tr>';
            echo '<td>' . htmlspecialchars($file) . '</td>';
            echo '<td>' . htmlspecialchars($desc) . '</td>';
            echo '<td>' . ($exists ? '✅ 是' : '❌ 否') . '</td>';
            echo '<td>' . ($exists ? number_format($size) . ' 字节' : '-') . '</td>';
            echo '<td>' . htmlspecialchars($mtime) . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        ?>

        <h2>🔧 服务器环境信息</h2>
        <div class="info-box">
            <strong>PHP版本:</strong> <?php echo PHP_VERSION; ?><br>
            <strong>服务器软件:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? '未知'; ?><br>
            <strong>文档根目录:</strong> <?php echo $_SERVER['DOCUMENT_ROOT'] ?? '未知'; ?><br>
            <strong>当前目录:</strong> <?php echo __DIR__; ?><br>
            <strong>当前时间:</strong> <?php echo date('Y-m-d H:i:s'); ?>
        </div>

        <h2>💾 缓存状态检查</h2>
        <?php
        // 检查OPcache
        if (function_exists('opcache_get_status')) {
            $opcache_status = opcache_get_status();
            if ($opcache_status !== false) {
                echo '<div class="info-box warning">';
                echo '<strong>⚠️ OPcache已启用</strong><br>';
                echo 'OPcache可能缓存了旧的PHP代码。<br>';
                echo '命中率: ' . round($opcache_status['opcache_statistics']['opcache_hit_rate'], 2) . '%<br>';
                echo '缓存的脚本数: ' . $opcache_status['opcache_statistics']['num_cached_scripts'];
                echo '</div>';
            } else {
                echo '<div class="info-box success"><strong>✅ OPcache未启用或已禁用</strong></div>';
            }
        } else {
            echo '<div class="info-box success"><strong>✅ OPcache不可用</strong></div>';
        }

        // 检查响应头
        echo '<h3>📡 HTTP响应头</h3>';
        echo '<div class="code">';
        foreach (headers_list() as $header) {
            echo htmlspecialchars($header) . '<br>';
        }
        echo '</div>';
        ?>

        <h2>🌐 浏览器缓存测试</h2>
        <div class="info-box">
            <strong>随机数测试:</strong> <?php echo rand(10000, 99999); ?><br>
            <strong>时间戳测试:</strong> <?php echo time(); ?><br>
            <em>如果每次刷新这些数字都变化，说明没有缓存问题</em>
        </div>

        <h2>🔍 文件内容检查</h2>
        <?php
        // 检查index.html的关键特征
        if (file_exists('index.html')) {
            $content = file_get_contents('index.html');
            
            // 检查是否包含最新功能的关键词
            $features_to_check = [
                'DOM缓存' => '性能优化功能',
                'database_import_tool' => '数据库导入工具',
                'download_backup' => '备份下载功能',
                'decreasing' => '递减记账功能',
                'monthly_states' => '月份状态功能'
            ];
            
            echo '<table>';
            echo '<tr><th>功能特征</th><th>描述</th><th>状态</th></tr>';
            
            foreach ($features_to_check as $keyword => $desc) {
                $found = strpos($content, $keyword) !== false;
                echo '<tr>';
                echo '<td>' . htmlspecialchars($keyword) . '</td>';
                echo '<td>' . htmlspecialchars($desc) . '</td>';
                echo '<td>' . ($found ? '✅ 存在' : '❌ 缺失') . '</td>';
                echo '</tr>';
            }
            echo '</table>';
            
            // 显示文件大小和修改时间
            echo '<div class="info-box">';
            echo '<strong>index.html 详细信息:</strong><br>';
            echo '文件大小: ' . number_format(strlen($content)) . ' 字符<br>';
            echo '修改时间: ' . date('Y-m-d H:i:s', filemtime('index.html')) . '<br>';
            echo '前100个字符: ' . htmlspecialchars(substr($content, 0, 100)) . '...';
            echo '</div>';
        } else {
            echo '<div class="info-box error"><strong>❌ index.html 文件不存在！</strong></div>';
        }
        ?>

        <h2>🛠️ 解决方案</h2>
        <div class="info-box">
            <h3>如果显示的是旧版本，请按顺序尝试：</h3>
            <ol>
                <li><strong>清除浏览器缓存:</strong> Ctrl+F5 或 Cmd+Shift+R</li>
                <li><strong>使用无痕模式:</strong> 测试是否是浏览器缓存问题</li>
                <li><strong>清除OPcache:</strong> 在宝塔面板重启PHP服务</li>
                <li><strong>检查文件上传:</strong> 确认所有文件都已正确上传</li>
                <li><strong>检查.htaccess:</strong> 确认配置文件已上传</li>
            </ol>
        </div>

        <h2>🔄 快速操作</h2>
        <a href="?clear_opcache=1" class="btn">清除OPcache</a>
        <a href="?" class="btn">重新检查</a>
        <a href="index.html" class="btn">访问主页</a>
        
        <?php
        // 处理清除OPcache请求
        if (isset($_GET['clear_opcache']) && function_exists('opcache_reset')) {
            if (opcache_reset()) {
                echo '<div class="info-box success"><strong>✅ OPcache已清除</strong></div>';
            } else {
                echo '<div class="info-box error"><strong>❌ OPcache清除失败</strong></div>';
            }
        }
        ?>

        <div class="info-box warning">
            <strong>💡 提示:</strong> 如果问题仍然存在，请检查：
            <ul>
                <li>是否有CDN或反向代理缓存</li>
                <li>宝塔面板是否启用了网站缓存插件</li>
                <li>域名DNS是否指向正确的服务器</li>
                <li>文件上传是否完整（包括隐藏文件）</li>
            </ul>
        </div>
    </div>

    <script>
        // 添加时间戳到页面，帮助识别缓存问题
        console.log('页面加载时间:', new Date().toLocaleString());
        console.log('随机数:', Math.random());
    </script>
</body>
</html>
