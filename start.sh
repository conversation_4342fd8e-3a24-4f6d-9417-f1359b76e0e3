#!/bin/bash

# 数据记账系统 - 服务器启动脚本
# 自动处理端口占用问题，一键启动服务器

PORT=8080
PROJECT_DIR="/home/<USER>/project"

echo "🚀 启动数据记账系统服务器..."

# 1. 清理端口占用
echo "🧹 清理端口 $PORT..."

# 方法1: 杀死PHP进程
pkill -f "php.*-S.*$PORT" 2>/dev/null
sleep 1

# 方法2: 使用netstat查找并杀死进程
if command -v netstat >/dev/null 2>&1; then
    # 先尝试普通权限
    PID=$(netstat -tlnp 2>/dev/null | grep ":$PORT " | awk '{print $7}' | cut -d'/' -f1)
    if [ ! -z "$PID" ] && [ "$PID" != "-" ]; then
        echo "   发现进程 $PID 占用端口，正在清理..."
        kill -9 "$PID" 2>/dev/null
        sleep 1
    else
        # 尝试sudo权限
        PID=$(sudo netstat -tlnp 2>/dev/null | grep ":$PORT " | awk '{print $7}' | cut -d'/' -f1)
        if [ ! -z "$PID" ] && [ "$PID" != "-" ]; then
            echo "   发现进程 $PID 占用端口，使用sudo清理..."
            sudo kill -9 "$PID" 2>/dev/null
            sleep 1
        fi
    fi
fi

# 方法3: 使用lsof（如果可用）
if command -v lsof >/dev/null 2>&1; then
    if lsof -ti:$PORT >/dev/null 2>&1; then
        echo "   使用lsof强制释放端口..."
        lsof -ti:$PORT | xargs kill -9 2>/dev/null
        sleep 1
    fi
fi

# 方法4: 使用fuser（如果可用）
if command -v fuser >/dev/null 2>&1; then
    echo "   使用fuser清理端口..."
    fuser -k $PORT/tcp 2>/dev/null
    sleep 1
fi

# 3. 进入项目目录
cd "$PROJECT_DIR" || {
    echo "❌ 无法进入项目目录: $PROJECT_DIR"
    exit 1
}

# 4. 启动服务器
echo "✅ 启动服务器在 http://localhost:$PORT"
echo "💡 按 Ctrl+C 停止服务器"
echo "🔄 如果服务器掉线，重新运行此脚本即可"
echo ""

# 启动PHP服务器，增加内存限制
php -d memory_limit=512M -d max_execution_time=300 -S 0.0.0.0:$PORT
