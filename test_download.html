<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>备份下载测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .backup-list {
            margin: 20px 0;
        }
        .backup-item {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .backup-name {
            font-weight: bold;
            color: #333;
        }
        .download-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .download-btn:hover {
            background: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 备份下载功能测试</h1>
        
        <div id="status" class="status"></div>
        
        <p>这个页面用于测试备份下载功能是否正常工作。</p>
        
        <div class="backup-list">
            <h3>📦 可用备份列表</h3>
            <div id="backup-list">
                <p>正在加载备份列表...</p>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #e9ecef; border-radius: 5px;">
            <h4>📋 测试说明</h4>
            <ul>
                <li><strong>tar.gz格式</strong>：如果系统支持tar命令，将下载压缩包</li>
                <li><strong>zip格式</strong>：如果系统支持zip命令，将下载zip文件</li>
                <li><strong>详细清单</strong>：如果压缩失败，将下载包含所有文件信息的文本文件</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <a href="backup_manager.html" class="download-btn">🔙 返回备份管理器</a>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'success') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            
            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 5000);
        }

        function testDownload(backupName) {
            showStatus(`正在测试下载: ${backupName}`, 'success');
            
            // 创建一个隐藏的iframe来测试下载
            const iframe = document.createElement('iframe');
            iframe.style.display = 'none';
            iframe.src = `download_backup.php?backup=${encodeURIComponent(backupName)}`;
            document.body.appendChild(iframe);
            
            // 5秒后移除iframe
            setTimeout(() => {
                document.body.removeChild(iframe);
            }, 5000);
        }

        async function loadBackupList() {
            try {
                const response = await fetch('backup_api.php?action=list_backups');
                const result = await response.json();
                
                if (result.success) {
                    displayBackupList(result.data);
                } else {
                    document.getElementById('backup-list').innerHTML = 
                        '<p style="color: red;">加载失败: ' + result.message + '</p>';
                }
            } catch (error) {
                document.getElementById('backup-list').innerHTML = 
                    '<p style="color: red;">网络错误: ' + error.message + '</p>';
            }
        }

        function displayBackupList(backups) {
            const listEl = document.getElementById('backup-list');
            
            if (backups.length === 0) {
                listEl.innerHTML = '<p>暂无备份文件</p>';
                return;
            }
            
            let html = '';
            backups.forEach(backup => {
                html += `
                    <div class="backup-item">
                        <div>
                            <div class="backup-name">${backup.name}</div>
                            <small>📅 ${backup.date} | 📦 ${backup.size}</small>
                        </div>
                        <button class="download-btn" onclick="testDownload('${backup.name}')">
                            🧪 测试下载
                        </button>
                    </div>
                `;
            });
            
            listEl.innerHTML = html;
        }

        // 页面加载时获取备份列表
        window.onload = function() {
            loadBackupList();
        };
    </script>
</body>
</html>
