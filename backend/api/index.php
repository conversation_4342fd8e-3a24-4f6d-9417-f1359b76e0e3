<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 获取请求路径
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);

// 移除 /api 前缀
$path = preg_replace('#^/api#', '', $path);

// 调试信息
error_log("API Request: " . $requestUri);
error_log("Parsed path: " . $path);

// 路由映射
if (preg_match('#^/auth(/.*)?$#', $path, $matches)) {
    $_SERVER['PATH_INFO'] = isset($matches[1]) ? $matches[1] : '';
    require_once __DIR__ . '/auth.php';
} elseif (preg_match('#^/account-books(/.*)?$#', $path, $matches)) {
    $_SERVER['PATH_INFO'] = isset($matches[1]) ? $matches[1] : '';
    require_once __DIR__ . '/account_books.php';
} elseif (preg_match('#^/records(/.*)?$#', $path, $matches)) {
    $_SERVER['PATH_INFO'] = isset($matches[1]) ? $matches[1] : '';
    require_once __DIR__ . '/records.php';
} elseif (preg_match('#^/statistics(/.*)?$#', $path, $matches)) {
    $_SERVER['PATH_INFO'] = isset($matches[1]) ? $matches[1] : '';
    require_once __DIR__ . '/statistics.php';
} elseif (preg_match('#^/export(/.*)?$#', $path, $matches)) {
    $_SERVER['PATH_INFO'] = isset($matches[1]) ? $matches[1] : '';
    require_once __DIR__ . '/export.php';
} else {
    http_response_code(404);
    echo json_encode([
        'error' => '接口不存在',
        'path' => $path,
        'request_uri' => $requestUri
    ]);
}
