<?php

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['PATH_INFO'] ?? '';

try {
    $user = verifyToken();
    $db = new Database();
    $pdo = $db->getConnection();

    switch ($method) {
        case 'GET':
            if (preg_match('/^\/(\d+)$/', $path, $matches)) {
                getRecords($pdo, $user['user_id'], $matches[1]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;

        case 'POST':
            if (preg_match('/^\/(\d+)$/', $path, $matches)) {
                createRecord($pdo, $user['user_id'], $matches[1]);
            } elseif (preg_match('/^\/(\d+)\/(\d+)\/toggle$/', $path, $matches)) {
                toggleRecord($pdo, $user['user_id'], $matches[1], $matches[2]);
            } elseif ($path === '/reset-monthly') {
                resetMonthlyRecords($pdo, $user['user_id']);
            } else {
                Response::error('未找到接口', 404);
            }
            break;

        case 'PUT':
            if (preg_match('/^\/(\d+)\/(\d+)$/', $path, $matches)) {
                updateRecord($pdo, $user['user_id'], $matches[1], $matches[2]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;

        case 'DELETE':
            if (preg_match('/^\/(\d+)\/(\d+)$/', $path, $matches)) {
                deleteRecord($pdo, $user['user_id'], $matches[1], $matches[2]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;

        default:
            Response::error('不支持的请求方法', 405);
    }
} catch (Exception $e) {
    Response::error($e->getMessage(), 500);
}

function getRecords($pdo, $userId, $bookId) {
    // 验证账本所有权
    $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
    $stmt->execute([$bookId, $userId]);
    if (!$stmt->fetch()) {
        Response::error('账本不存在或无权限', 404);
    }

    $stmt = $pdo->prepare("
        SELECT * FROM records
        WHERE account_book_id = ?
        ORDER BY date DESC, created_at DESC
    ");
    $stmt->execute([$bookId]);
    $records = $stmt->fetchAll();

    Response::success($records);
}

function createRecord($pdo, $userId, $bookId) {
    // 验证账本所有权
    $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
    $stmt->execute([$bookId, $userId]);
    if (!$stmt->fetch()) {
        Response::error('账本不存在或无权限', 404);
    }

    $data = getRequestData();

    // 验证必填字段
    $required = ['date', 'name', 'amount', 'monthly_amount', 'renewal_time', 'renewal_amount'];
    foreach ($required as $field) {
        if (!isset($data[$field]) || $data[$field] === '') {
            Response::error("字段 {$field} 不能为空");
        }
    }

    // 验证续期时间选项
    $validRenewalTimes = ['二个月', '三个月', '六个月', '永久'];
    if (!in_array($data['renewal_time'], $validRenewalTimes)) {
        Response::error('续期时间选项无效');
    }

    $stmt = $pdo->prepare("
        INSERT INTO records (
            account_book_id, date, name, amount, monthly_amount,
            renewal_time, renewal_amount, remark, accumulated_amount
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0)
    ");

    $stmt->execute([
        $bookId,
        $data['date'],
        $data['name'],
        $data['amount'],
        $data['monthly_amount'],
        $data['renewal_time'],
        $data['renewal_amount'],
        $data['remark'] ?? ''
    ]);

    $recordId = $pdo->lastInsertId();

    $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
    $stmt->execute([$recordId]);
    $record = $stmt->fetch();

    Response::success($record, '记录创建成功');
}

function updateRecord($pdo, $userId, $bookId, $recordId) {
    // 验证权限
    $stmt = $pdo->prepare("
        SELECT r.* FROM records r
        JOIN account_books ab ON r.account_book_id = ab.id
        WHERE r.id = ? AND r.account_book_id = ? AND ab.user_id = ?
    ");
    $stmt->execute([$recordId, $bookId, $userId]);
    $record = $stmt->fetch();

    if (!$record) {
        Response::error('记录不存在或无权限', 404);
    }

    $data = getRequestData();

    // 验证必填字段
    $required = ['date', 'name', 'amount', 'monthly_amount', 'renewal_time', 'renewal_amount'];
    foreach ($required as $field) {
        if (!isset($data[$field]) || $data[$field] === '') {
            Response::error("字段 {$field} 不能为空");
        }
    }

    $stmt = $pdo->prepare("
        UPDATE records SET
            date = ?, name = ?, amount = ?, monthly_amount = ?,
            renewal_time = ?, renewal_amount = ?, remark = ?
        WHERE id = ?
    ");

    $stmt->execute([
        $data['date'],
        $data['name'],
        $data['amount'],
        $data['monthly_amount'],
        $data['renewal_time'],
        $data['renewal_amount'],
        $data['remark'] ?? '',
        $recordId
    ]);

    $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
    $stmt->execute([$recordId]);
    $record = $stmt->fetch();

    Response::success($record, '记录更新成功');
}

function deleteRecord($pdo, $userId, $bookId, $recordId) {
    // 验证权限
    $stmt = $pdo->prepare("
        SELECT r.* FROM records r
        JOIN account_books ab ON r.account_book_id = ab.id
        WHERE r.id = ? AND r.account_book_id = ? AND ab.user_id = ?
    ");
    $stmt->execute([$recordId, $bookId, $userId]);
    $record = $stmt->fetch();

    if (!$record) {
        Response::error('记录不存在或无权限', 404);
    }

    $stmt = $pdo->prepare("DELETE FROM records WHERE id = ?");
    $stmt->execute([$recordId]);

    Response::success(null, '记录删除成功');
}

function toggleRecord($pdo, $userId, $bookId, $recordId) {
    // 验证权限
    $stmt = $pdo->prepare("
        SELECT r.* FROM records r
        JOIN account_books ab ON r.account_book_id = ab.id
        WHERE r.id = ? AND r.account_book_id = ? AND ab.user_id = ?
    ");
    $stmt->execute([$recordId, $bookId, $userId]);
    $record = $stmt->fetch();

    if (!$record) {
        Response::error('记录不存在或无权限', 404);
    }

    $currentMonth = date('Y-m');
    $newStatus = !$record['is_completed'];

    // 如果记录已被锁定且不是当月操作，不允许修改
    if ($record['is_locked'] && $record['completed_month'] !== $currentMonth) {
        Response::error('该记录已被月度锁定，无法修改');
    }

    // 计算新的累计金额
    $newAccumulatedAmount = $record['accumulated_amount'];
    if ($newStatus) {
        // 标记为完成
        $newAccumulatedAmount += $record['monthly_amount'];
        $completedMonth = $currentMonth;
    } else {
        // 取消完成
        if ($record['completed_month'] === $currentMonth) {
            // 当月操作，可以减少累计金额
            $newAccumulatedAmount -= $record['monthly_amount'];
        }
        $completedMonth = null;
    }

    $stmt = $pdo->prepare("
        UPDATE records SET
            is_completed = ?,
            accumulated_amount = ?,
            completed_month = ?
        WHERE id = ?
    ");
    $stmt->execute([$newStatus, $newAccumulatedAmount, $completedMonth, $recordId]);

    $stmt = $pdo->prepare("SELECT * FROM records WHERE id = ?");
    $stmt->execute([$recordId]);
    $record = $stmt->fetch();

    Response::success($record, '状态更新成功');
}

function resetMonthlyRecords($pdo, $userId) {
    // 获取用户的所有账本
    $stmt = $pdo->prepare("SELECT id FROM account_books WHERE user_id = ?");
    $stmt->execute([$userId]);
    $books = $stmt->fetchAll();

    $bookIds = array_column($books, 'id');
    if (empty($bookIds)) {
        Response::success(null, '没有需要重置的记录');
    }

    $placeholders = str_repeat('?,', count($bookIds) - 1) . '?';

    // 锁定上个月的记录
    $lastMonth = date('Y-m', strtotime('-1 month'));
    $stmt = $pdo->prepare("
        UPDATE records SET is_locked = 1
        WHERE account_book_id IN ($placeholders)
        AND completed_month = ?
    ");
    $stmt->execute(array_merge($bookIds, [$lastMonth]));

    // 重置当月记录（取消勾选）
    $stmt = $pdo->prepare("
        UPDATE records SET
            is_completed = 0,
            completed_month = NULL
        WHERE account_book_id IN ($placeholders)
        AND is_completed = 1
        AND is_locked = 0
    ");
    $stmt->execute($bookIds);

    Response::success(null, '月度重置完成');
}
