<?php

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['PATH_INFO'] ?? '';

try {
    $db = new Database();
    $pdo = $db->getConnection();
    
    switch ($method) {
        case 'POST':
            if ($path === '/register') {
                register($pdo);
            } elseif ($path === '/login') {
                login($pdo);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        default:
            Response::error('不支持的请求方法', 405);
    }
} catch (Exception $e) {
    Response::error($e->getMessage(), 500);
}

function register($pdo) {
    $data = getRequestData();
    
    // 验证输入
    if (empty($data['username']) || empty($data['email']) || empty($data['password'])) {
        Response::error('用户名、邮箱和密码不能为空');
    }
    
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        Response::error('邮箱格式不正确');
    }
    
    if (strlen($data['password']) < 6) {
        Response::error('密码长度不能少于6位');
    }
    
    // 检查用户名和邮箱是否已存在
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$data['username'], $data['email']]);
    if ($stmt->fetch()) {
        Response::error('用户名或邮箱已存在');
    }
    
    // 创建用户
    $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (username, email, password) VALUES (?, ?, ?)");
    $stmt->execute([$data['username'], $data['email'], $hashedPassword]);
    
    $userId = $pdo->lastInsertId();
    
    // 创建默认账本
    $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
    $stmt->execute([$userId, '默认账本', '系统自动创建的默认账本']);
    
    // 生成 JWT
    $payload = [
        'user_id' => $userId,
        'username' => $data['username'],
        'exp' => time() + (7 * 24 * 60 * 60) // 7天过期
    ];
    $token = JWT::encode($payload);
    
    Response::success([
        'token' => $token,
        'user' => [
            'id' => $userId,
            'username' => $data['username'],
            'email' => $data['email']
        ]
    ], '注册成功');
}

function login($pdo) {
    $data = getRequestData();
    
    // 验证输入
    if (empty($data['username']) || empty($data['password'])) {
        Response::error('用户名和密码不能为空');
    }
    
    // 查找用户
    $stmt = $pdo->prepare("SELECT id, username, email, password FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$data['username'], $data['username']]);
    $user = $stmt->fetch();
    
    if (!$user || !password_verify($data['password'], $user['password'])) {
        Response::error('用户名或密码错误');
    }
    
    // 生成 JWT
    $payload = [
        'user_id' => $user['id'],
        'username' => $user['username'],
        'exp' => time() + (7 * 24 * 60 * 60) // 7天过期
    ];
    $token = JWT::encode($payload);
    
    Response::success([
        'token' => $token,
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email']
        ]
    ], '登录成功');
}
