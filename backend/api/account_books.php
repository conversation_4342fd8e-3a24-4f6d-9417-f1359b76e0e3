<?php

require_once 'config.php';

$method = $_SERVER['REQUEST_METHOD'];
$path = $_SERVER['PATH_INFO'] ?? '';

try {
    $user = verifyToken();
    $db = new Database();
    $pdo = $db->getConnection();
    
    switch ($method) {
        case 'GET':
            if ($path === '' || $path === '/') {
                getAccountBooks($pdo, $user['user_id']);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        case 'POST':
            if ($path === '' || $path === '/') {
                createAccountBook($pdo, $user['user_id']);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        case 'PUT':
            if (preg_match('/^\/(\d+)$/', $path, $matches)) {
                updateAccountBook($pdo, $user['user_id'], $matches[1]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        case 'DELETE':
            if (preg_match('/^\/(\d+)$/', $path, $matches)) {
                deleteAccountBook($pdo, $user['user_id'], $matches[1]);
            } else {
                Response::error('未找到接口', 404);
            }
            break;
            
        default:
            Response::error('不支持的请求方法', 405);
    }
} catch (Exception $e) {
    Response::error($e->getMessage(), 500);
}

function getAccountBooks($pdo, $userId) {
    $stmt = $pdo->prepare("
        SELECT ab.*, 
               COUNT(r.id) as record_count,
               COALESCE(SUM(r.accumulated_amount), 0) as total_amount
        FROM account_books ab 
        LEFT JOIN records r ON ab.id = r.account_book_id 
        WHERE ab.user_id = ? 
        GROUP BY ab.id 
        ORDER BY ab.created_at DESC
    ");
    $stmt->execute([$userId]);
    $books = $stmt->fetchAll();
    
    Response::success($books);
}

function createAccountBook($pdo, $userId) {
    $data = getRequestData();
    
    if (empty($data['name'])) {
        Response::error('账本名称不能为空');
    }
    
    $stmt = $pdo->prepare("INSERT INTO account_books (user_id, name, description) VALUES (?, ?, ?)");
    $stmt->execute([$userId, $data['name'], $data['description'] ?? '']);
    
    $bookId = $pdo->lastInsertId();
    
    $stmt = $pdo->prepare("SELECT * FROM account_books WHERE id = ?");
    $stmt->execute([$bookId]);
    $book = $stmt->fetch();
    
    Response::success($book, '账本创建成功');
}

function updateAccountBook($pdo, $userId, $bookId) {
    $data = getRequestData();
    
    // 验证账本所有权
    $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
    $stmt->execute([$bookId, $userId]);
    if (!$stmt->fetch()) {
        Response::error('账本不存在或无权限', 404);
    }
    
    if (empty($data['name'])) {
        Response::error('账本名称不能为空');
    }
    
    $stmt = $pdo->prepare("UPDATE account_books SET name = ?, description = ? WHERE id = ?");
    $stmt->execute([$data['name'], $data['description'] ?? '', $bookId]);
    
    $stmt = $pdo->prepare("SELECT * FROM account_books WHERE id = ?");
    $stmt->execute([$bookId]);
    $book = $stmt->fetch();
    
    Response::success($book, '账本更新成功');
}

function deleteAccountBook($pdo, $userId, $bookId) {
    // 验证账本所有权
    $stmt = $pdo->prepare("SELECT id FROM account_books WHERE id = ? AND user_id = ?");
    $stmt->execute([$bookId, $userId]);
    if (!$stmt->fetch()) {
        Response::error('账本不存在或无权限', 404);
    }
    
    // 检查是否为用户唯一账本
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM account_books WHERE user_id = ?");
    $stmt->execute([$userId]);
    $count = $stmt->fetch()['count'];
    
    if ($count <= 1) {
        Response::error('不能删除唯一的账本');
    }
    
    $stmt = $pdo->prepare("DELETE FROM account_books WHERE id = ?");
    $stmt->execute([$bookId]);
    
    Response::success(null, '账本删除成功');
}
